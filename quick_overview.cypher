// 快速查看Twitter数据全貌

// 1. 【数据概览】- 查看有多少用户和朋友
MATCH (ego:EgoNode)
OPTIONAL MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
RETURN ego.dataset as TwitterUser, 
       count(friend) as FriendsCount,
       size(ego.active_features) as ActiveFeatures
ORDER BY FriendsCount DESC
LIMIT 10;

// 2. 【完整网络展示】- 选择一个用户展示完整社交网络
MATCH (ego:EgoNode {dataset: '12831'})  // 可以改成其他用户ID
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
OPTIONAL MATCH (friend)-[friendship:FRIEND_OF]-(other_friend:Node)
WHERE other_friend.dataset = ego.dataset
RETURN ego, friend, friendship, other_friend;

// 3. 【随机网络样本】- 随机选择一个用户展示网络
MATCH (ego:EgoNode)
WITH ego, rand() as r
ORDER BY r LIMIT 1
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
WITH ego, collect(friend) as all_friends
WITH ego, all_friends[0..15] as sample_friends  // 只取前15个朋友避免过于复杂
UNWIND sample_friends as friend
OPTIONAL MATCH (friend)-[friendship:FRIEND_OF]-(other_friend:Node)
WHERE other_friend IN sample_friends
RETURN ego.dataset as User,
       ego, friend, friendship, other_friend;

// 4. 【社交圈子展示】- 展示用户的社交圈子
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)-[:BELONGS_TO]->(circle:Circle)
WITH ego, circle, collect(friend) as circle_members
RETURN ego.dataset as User,
       circle.circle_id as CircleID,
       size(circle_members) as MembersCount,
       circle_members[0..5] as SampleMembers
ORDER BY MembersCount DESC
LIMIT 20;

// 5. 【网络密度分析】- 查看朋友之间的连接密度
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as total_friends
WHERE total_friends > 5  // 只看有5个以上朋友的用户
MATCH (ego)-[:HAS_FRIEND]->(f1:Node)
MATCH (ego)-[:HAS_FRIEND]->(f2:Node)
OPTIONAL MATCH (f1)-[:FRIEND_OF]-(f2)
WHERE f1.id < f2.id
WITH ego, total_friends, count(*) as friend_pairs, count(f1) as connected_pairs
RETURN ego.dataset as User,
       total_friends,
       connected_pairs,
       friend_pairs,
       round(connected_pairs * 100.0 / friend_pairs, 1) as DensityPercent
ORDER BY DensityPercent DESC
LIMIT 10;
