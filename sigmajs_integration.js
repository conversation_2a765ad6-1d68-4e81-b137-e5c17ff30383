// Sigma.js 集成代码 - 将Neo4j数据转换为Sigma.js格式并渲染

// 1. Neo4j查询函数
async function fetchNetworkData() {
    // 这里替换为您的Neo4j API调用
    const query = `
        MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
        WITH ego, count(friend) as friend_count
        WHERE friend_count > 8 AND friend_count < 20
        ORDER BY friend_count DESC
        LIMIT 20

        MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
        WITH ego, collect(friend)[0..20] as sample_friends
        UNWIND sample_friends as friend
        OPTIONAL MATCH (friend)-[rel:FRIEND_OF]-(other:Node)
        WHERE other IN sample_friends
        OPTIONAL MATCH (friend)-[:BELONGS_TO]->(circle:Circle)

        WITH ego, friend, other, rel, circle,
             collect(DISTINCT {
               id: ego.id,
               label: 'EGO',
               dataset: ego.dataset,
               type: 'ego',
               features: ego.active_features[0..3]
             }) as ego_nodes,
             collect(DISTINCT {
               id: friend.id,
               label: 'Friend',
               dataset: friend.dataset,
               type: 'friend',
               features: friend.active_features[0..2]
             }) as friend_nodes,
             collect(DISTINCT {
               id: other.id,
               label: 'Friend',
               dataset: other.dataset,
               type: 'friend',
               features: other.active_features[0..2]
             }) as other_nodes,
             collect(DISTINCT {
               id: 'circle_' + circle.dataset + '_' + circle.circle_id,
               label: 'Circle ' + circle.circle_id,
               dataset: circle.dataset,
               type: 'circle',
               circle_id: circle.circle_id,
               size: circle.size
             }) as circle_nodes

        RETURN {
          nodes: ego_nodes + friend_nodes + other_nodes + circle_nodes,
          edges: [
            {
              source: ego.id,
              target: friend.id,
              type: 'HAS_FRIEND',
              dataset: ego.dataset
            }
          ] + CASE WHEN other IS NOT NULL THEN [
            {
              source: friend.id,
              target: other.id,
              type: 'FRIEND_OF',
              dataset: friend.dataset
            }
          ] ELSE [] END + CASE WHEN circle IS NOT NULL THEN [
            {
              source: friend.id,
              target: 'circle_' + circle.dataset + '_' + circle.circle_id,
              type: 'BELONGS_TO',
              dataset: circle.dataset
            }
          ] ELSE [] END
        } as graph_data;
    `;
    
    // 模拟API调用，实际使用时替换为真实的Neo4j API
    const response = await fetch('/api/neo4j/query', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query })
    });
    
    return await response.json();
}

// 2. 数据转换函数：Neo4j格式 -> Sigma.js格式
function convertToSigmaFormat(neo4jData) {
    const sigmaData = {
        nodes: [],
        edges: []
    };
    
    // 处理所有记录
    neo4jData.forEach((record, recordIndex) => {
        const graphData = record.graph_data;
        
        // 转换节点
        graphData.nodes.forEach(node => {
            if (!sigmaData.nodes.find(n => n.key === String(node.id))) {
                sigmaData.nodes.push({
                    key: String(node.id),
                    label: node.label || String(node.id),
                    tag: node.type,
                    dataset: node.dataset,
                    features: node.features || [],
                    // Sigma.js 样式属性
                    x: Math.random() * 1000,
                    y: Math.random() * 1000,
                    size: node.type === 'ego' ? 15 : (node.type === 'circle' ? 12 : 8),
                    color: node.type === 'ego' ? '#3498db' : (node.type === 'circle' ? '#9b59b6' : '#e74c3c')
                });
            }
        });
        
        // 转换边
        graphData.edges.forEach((edge, edgeIndex) => {
            if (edge.source && edge.target) {
                sigmaData.edges.push({
                    key: `edge_${recordIndex}_${edgeIndex}`,
                    source: String(edge.source),
                    target: String(edge.target),
                    label: edge.type,
                    type: edge.type,
                    dataset: edge.dataset,
                    // Sigma.js 样式属性
                    color: edge.type === 'HAS_FRIEND' ? '#2980b9' : (edge.type === 'BELONGS_TO' ? '#8e44ad' : '#95a5a6'),
                    size: edge.type === 'HAS_FRIEND' ? 3 : (edge.type === 'BELONGS_TO' ? 2 : 1)
                });
            }
        });
    });
    
    return sigmaData;
}

// 3. Sigma.js 渲染函数
function renderSigmaGraph(container, data) {
    // 清除现有图形
    if (window.sigmaInstance) {
        window.sigmaInstance.kill();
    }
    
    // 创建Sigma实例
    window.sigmaInstance = new Sigma({
        graph: data,
        container: container,
        settings: {
            // 节点设置
            defaultNodeColor: '#e74c3c',
            defaultNodeBorderColor: '#c0392b',
            nodesPowRatio: 0.8,
            nodesSpeed: 3,
            
            // 边设置
            defaultEdgeColor: '#95a5a6',
            edgesPowRatio: 0.8,
            edgesSpeed: 3,
            
            // 标签设置
            drawLabels: true,
            labelThreshold: 8,
            labelSize: 'proportional',
            labelSizeRatio: 2,
            
            // 交互设置
            mouseWheelEnabled: true,
            doubleClickEnabled: true,
            minNodeSize: 4,
            maxNodeSize: 20,
            minEdgeSize: 1,
            maxEdgeSize: 5,
            
            // 动画设置
            animationsTime: 1000
        }
    });
    
    // 添加事件监听器
    window.sigmaInstance.bind('clickNode', function(e) {
        const node = e.data.node;
        console.log('Clicked node:', node);
        
        // 显示节点详情
        showNodeDetails(node);
    });
    
    window.sigmaInstance.bind('clickEdge', function(e) {
        const edge = e.data.edge;
        console.log('Clicked edge:', edge);
    });
    
    // 启动布局算法
    startForceAtlas2();
    
    return window.sigmaInstance;
}

// 4. 启动ForceAtlas2布局算法
function startForceAtlas2() {
    if (window.sigmaInstance) {
        window.sigmaInstance.startForceAtlas2({
            worker: true,
            barnesHutOptimize: true,
            barnesHutTheta: 0.5,
            iterationsPerRender: 1,
            linLogMode: false,
            outboundAttractionDistribution: false,
            adjustSizes: false,
            edgeWeightInfluence: 1,
            scalingRatio: 1,
            strongGravityMode: false,
            gravity: 1
        });
        
        // 10秒后停止布局
        setTimeout(() => {
            if (window.sigmaInstance) {
                window.sigmaInstance.stopForceAtlas2();
            }
        }, 10000);
    }
}

// 5. 显示节点详情
function showNodeDetails(node) {
    let detailsHtml = `
        <div class="node-details">
            <h3>节点详情</h3>
            <p><strong>ID:</strong> ${node.label}</p>
            <p><strong>类型:</strong> ${node.tag}</p>
            <p><strong>数据集:</strong> ${node.dataset}</p>
    `;

    if (node.tag === 'circle') {
        detailsHtml += `
            <p><strong>圈子ID:</strong> ${node.circle_id || 'N/A'}</p>
            <p><strong>圈子大小:</strong> ${node.size || 'N/A'}</p>
        `;
    } else if (node.features) {
        detailsHtml += `
            <p><strong>特征:</strong> ${node.features.join(', ')}</p>
        `;
    }

    detailsHtml += '</div>';

    // 显示在侧边栏或弹窗中
    document.getElementById('node-details').innerHTML = detailsHtml;
}

// 6. 主初始化函数
async function initTwitterNetwork() {
    try {
        // 获取数据
        console.log('正在获取网络数据...');
        const neo4jData = await fetchNetworkData();
        
        // 转换数据格式
        console.log('正在转换数据格式...');
        const sigmaData = convertToSigmaFormat(neo4jData);
        
        console.log('Sigma数据:', sigmaData);
        console.log(`节点数: ${sigmaData.nodes.length}, 边数: ${sigmaData.edges.length}`);
        
        // 渲染图形
        console.log('正在渲染图形...');
        const container = document.getElementById('sigma-container');
        renderSigmaGraph(container, sigmaData);
        
        console.log('Twitter网络图形渲染完成！');
        
    } catch (error) {
        console.error('初始化失败:', error);
    }
}

// 7. 工具函数
function resetGraph() {
    if (window.sigmaInstance) {
        window.sigmaInstance.graph.clear();
        window.sigmaInstance.refresh();
    }
}

function zoomToFit() {
    if (window.sigmaInstance) {
        window.sigmaInstance.camera.goTo({ x: 0, y: 0, angle: 0, ratio: 1 });
    }
}

// 导出函数供HTML使用
window.initTwitterNetwork = initTwitterNetwork;
window.resetGraph = resetGraph;
window.zoomToFit = zoomToFit;
