// 检查关系问题 - 诊断为什么看不到关系

// ========== 1. 检查是否有任何关系 ==========
MATCH ()-[r]->() 
RETURN type(r) as relationship_type, count(r) as count
ORDER BY count DESC;

// ========== 2. 检查具体的关系类型 ==========
// 检查HAS_FRIEND关系
MATCH ()-[r:HAS_FRIEND]->() 
RETURN count(r) as has_friend_count;

// 检查FRIEND_OF关系
MATCH ()-[r:FRIEND_OF]->() 
RETURN count(r) as friend_of_count;

// 检查BELONGS_TO关系
MATCH ()-[r:BELONGS_TO]->() 
RETURN count(r) as belongs_to_count;

// ========== 3. 检查节点是否有dataset属性匹配问题 ==========
// 检查EGO节点的dataset值
MATCH (ego:EgoNode) 
RETURN DISTINCT ego.dataset as ego_datasets 
ORDER BY ego_datasets 
LIMIT 10;

// 检查Node节点的dataset值
MATCH (friend:Node) 
RETURN DISTINCT friend.dataset as friend_datasets 
ORDER BY friend_datasets 
LIMIT 10;

// ========== 4. 检查特定数据集的节点 ==========
// 看看12831数据集有哪些节点
MATCH (n {dataset: '12831'}) 
RETURN labels(n) as node_type, count(n) as count;

// ========== 5. 尝试手动创建一个简单关系来测试 ==========
// 查找两个同数据集的节点
MATCH (ego:EgoNode), (friend:Node)
WHERE ego.dataset = friend.dataset
RETURN ego.dataset, ego.id, friend.id
LIMIT 5;

// ========== 6. 检查节点ID类型问题 ==========
// 检查EGO节点的ID类型
MATCH (ego:EgoNode)
RETURN ego.dataset, ego.id, type(ego.id) as id_type
LIMIT 5;

// 检查Friend节点的ID类型
MATCH (friend:Node)
RETURN friend.dataset, friend.id, type(friend.id) as id_type
LIMIT 5;

// ========== 7. 检查是否是ID匹配问题 ==========
// 看看EGO的ID是否等于dataset
MATCH (ego:EgoNode)
WHERE toString(ego.id) = ego.dataset
RETURN ego.dataset, ego.id
LIMIT 5;

// ========== 8. 简单的路径查询 ==========
// 尝试找任何两个节点之间的路径
MATCH p = (a)-[*1..2]-(b)
WHERE a <> b
RETURN p
LIMIT 5;
