// 展示实际节点数据 - 查看具体的节点信息而非统计

// ========== 1. 展示所有EGO节点的详细信息 ==========
MATCH (ego:EgoNode)
RETURN ego.id as EgoUserID,
       ego.dataset as Dataset,
       ego.active_features[0..5] as SampleActiveFeatures,
       size(ego.active_features) as TotalActiveFeatures,
       ego.feature_count as TotalFeatureCount
ORDER BY ego.dataset
LIMIT 20;

// ========== 2. 展示特定用户的所有朋友节点 ==========
MATCH (ego:EgoNode {dataset: '12831'})-[:HAS_FRIEND]->(friend:Node)
RETURN ego.id as EgoID,
       friend.id as FriendID,
       friend.active_features[0..3] as FriendFeatures,
       size(friend.active_features) as FriendFeatureCount
ORDER BY friend.id
LIMIT 30;

// ========== 3. 展示朋友之间的实际关系 ==========
MATCH (ego:EgoNode {dataset: '12831'})-[:HAS_FRIEND]->(friend1:Node)
MATCH (ego)-[:HAS_FRIEND]->(friend2:Node)
MATCH (friend1)-[rel:FRIEND_OF]-(friend2)
WHERE friend1.id < friend2.id
RETURN ego.dataset as User,
       friend1.id as Friend1,
       friend2.id as Friend2,
       type(rel) as RelationType,
       friend1.active_features[0..2] as Friend1Features,
       friend2.active_features[0..2] as Friend2Features
LIMIT 20;

// ========== 4. 展示社交圈子中的实际成员 ==========
MATCH (ego:EgoNode {dataset: '12831'})-[:HAS_FRIEND]->(friend:Node)-[:BELONGS_TO]->(circle:Circle)
WITH circle, collect(friend) as members
RETURN circle.dataset as User,
       circle.circle_id as CircleID,
       circle.size as CircleSize,
       [member IN members | {
           id: member.id, 
           features: member.active_features[0..2]
       }] as CircleMembers
ORDER BY circle.circle_id
LIMIT 10;

// ========== 5. 展示特征名称的实际内容 ==========
MATCH (feat:FeatureName {dataset: '12831'})
RETURN feat.index as FeatureIndex,
       feat.name as FeatureName,
       feat.dataset as Dataset
ORDER BY feat.index
LIMIT 20;

// ========== 6. 展示完整的网络结构（小样本） ==========
MATCH (ego:EgoNode)
WITH ego LIMIT 1
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
WITH ego, collect(friend)[0..8] as sample_friends
UNWIND sample_friends as friend
OPTIONAL MATCH (friend)-[rel:FRIEND_OF]-(other_friend:Node)
WHERE other_friend IN sample_friends
RETURN {
    ego: {
        id: ego.id,
        dataset: ego.dataset,
        features: ego.active_features[0..3]
    },
    friend: {
        id: friend.id,
        features: friend.active_features[0..2]
    },
    other_friend: CASE 
        WHEN other_friend IS NOT NULL 
        THEN {
            id: other_friend.id,
            features: other_friend.active_features[0..2]
        }
        ELSE null
    END,
    relationship: type(rel)
} as NetworkData;

// ========== 7. 展示用户的完整特征信息 ==========
MATCH (ego:EgoNode {dataset: '12831'})
MATCH (feat:FeatureName {dataset: '12831'})
WHERE feat.name IN ego.active_features
RETURN ego.id as UserID,
       feat.index as FeatureIndex,
       feat.name as FeatureName
ORDER BY feat.index
LIMIT 30;

// ========== 8. 展示多个用户的对比数据 ==========
MATCH (ego:EgoNode)
WITH ego ORDER BY ego.dataset LIMIT 5
OPTIONAL MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
WITH ego, collect(friend)[0..3] as sample_friends
RETURN {
    user: {
        id: ego.id,
        dataset: ego.dataset,
        active_features: ego.active_features[0..3]
    },
    friends: [f IN sample_friends | {
        id: f.id,
        features: f.active_features[0..2]
    }]
} as UserData;

// ========== 9. 展示原始特征向量数据 ==========
MATCH (ego:EgoNode {dataset: '12831'})
RETURN ego.id as UserID,
       ego.features[0..20] as FeatureVector,
       ego.active_features as ActiveFeatureNames
LIMIT 1;

// ========== 10. 展示节点的所有属性 ==========
MATCH (ego:EgoNode {dataset: '12831'})
RETURN ego as CompleteEgoNode
LIMIT 1;

MATCH (ego:EgoNode {dataset: '12831'})-[:HAS_FRIEND]->(friend:Node)
RETURN friend as CompleteFriendNode
LIMIT 3;
