<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Twitter社交网络可视化</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 300px;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            overflow-y: auto;
        }
        
        .main-content {
            flex: 1;
            position: relative;
        }
        
        #sigma-container {
            width: 100%;
            height: 100%;
            background-color: #ecf0f1;
        }
        
        .controls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: #2980b9;
        }
        
        .btn.danger {
            background-color: #e74c3c;
        }
        
        .btn.danger:hover {
            background-color: #c0392b;
        }
        
        .stats {
            background-color: #34495e;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .stats h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .stats p {
            margin: 5px 0;
            color: #bdc3c7;
        }
        
        #node-details {
            background-color: #34495e;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        
        #node-details h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        #node-details p {
            margin: 8px 0;
            color: #bdc3c7;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0,0,0,0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 2000;
        }
        
        .legend {
            background-color: #34495e;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .legend h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .ego-color { background-color: #3498db; }
        .friend-color { background-color: #e74c3c; }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h2>Twitter网络分析</h2>
            
            <div class="stats">
                <h3>网络统计</h3>
                <p>节点数: <span id="node-count">-</span></p>
                <p>边数: <span id="edge-count">-</span></p>
                <p>网络簇: <span id="cluster-count">-</span></p>
            </div>
            
            <div class="legend">
                <h3>图例</h3>
                <div class="legend-item">
                    <div class="legend-color ego-color"></div>
                    <span>EGO用户（中心节点）</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color friend-color"></div>
                    <span>朋友节点</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #9b59b6;"></div>
                    <span>社交圈子</span>
                </div>
            </div>
            
            <div id="node-details">
                <h3>节点详情</h3>
                <p>点击节点查看详细信息</p>
            </div>
        </div>
        
        <div class="main-content">
            <div class="controls">
                <button class="btn" onclick="initTwitterNetwork()">加载网络</button>
                <button class="btn" onclick="zoomToFit()">适应窗口</button>
                <button class="btn danger" onclick="resetGraph()">重置图形</button>
            </div>
            
            <div id="sigma-container"></div>
            
            <div id="loading" class="loading" style="display: none;">
                正在加载网络数据...
            </div>
        </div>
    </div>

    <!-- Sigma.js 库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sigma.js/1.2.1/sigma.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sigma.js/1.2.1/plugins/sigma.layout.forceAtlas2.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script src="sigmajs_integration.js"></script>
    
    <script>
        // 页面加载完成后自动初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，准备初始化网络...');
            // 可以选择自动加载或等待用户点击
            // initTwitterNetwork();
        });
        
        // 更新统计信息
        function updateStats(nodeCount, edgeCount, clusterCount) {
            document.getElementById('node-count').textContent = nodeCount;
            document.getElementById('edge-count').textContent = edgeCount;
            document.getElementById('cluster-count').textContent = clusterCount;
        }
        
        // 显示/隐藏加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        // 重写初始化函数以包含UI更新
        const originalInit = window.initTwitterNetwork;
        window.initTwitterNetwork = async function() {
            showLoading(true);
            try {
                await originalInit();
                // 假设数据已加载，更新统计信息
                if (window.sigmaInstance) {
                    const nodeCount = window.sigmaInstance.graph.nodes().length;
                    const edgeCount = window.sigmaInstance.graph.edges().length;
                    const clusterCount = 3; // 根据查询设置
                    updateStats(nodeCount, edgeCount, clusterCount);
                }
            } finally {
                showLoading(false);
            }
        };
    </script>
</body>
</html>
