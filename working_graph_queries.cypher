// 有效的图形查询 - 既然关系存在，用这些查询显示图形

// ========== 1. 最简单的关系显示 ==========
// 显示HAS_FRIEND关系
MATCH p = (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
RETURN p
LIMIT 20;

// ========== 2. 显示朋友之间的关系 ==========
// 显示FRIEND_OF关系
MATCH p = (friend1:Node)-[:FRIEND_OF]-(friend2:Node)
RETURN p
LIMIT 25;

// ========== 3. 显示社交圈子关系 ==========
// 显示BELONGS_TO关系
MATCH p = (friend:Node)-[:BELONGS_TO]->(circle:Circle)
RETURN p
LIMIT 25;

// ========== 4. 显示特定用户的网络 ==========
// 选择一个用户显示其朋友网络
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
ORDER BY friend_count DESC
LIMIT 1
MATCH p = (ego)-[:HAS_FRIEND]->(friend:Node)
RETURN p
LIMIT 30;

// ========== 5. 显示完整的三层网络 ==========
// EGO -> 朋友 -> 圈子
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)-[:BELONGS_TO]->(circle:Circle)
RETURN ego, friend, circle
LIMIT 20;

// ========== 6. 显示朋友关系网络 ==========
// 显示朋友之间如何相互连接
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend1:Node)
MATCH (ego)-[:HAS_FRIEND]->(friend2:Node)
MATCH p = (friend1)-[:FRIEND_OF]-(friend2)
RETURN p
LIMIT 25;

// ========== 7. 综合网络显示 ==========
// 显示一个用户的完整社交网络
MATCH (ego:EgoNode)
WITH ego LIMIT 1
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
WITH ego, collect(friend)[0..10] as sample_friends
UNWIND sample_friends as friend
MATCH p1 = (ego)-[:HAS_FRIEND]->(friend)
OPTIONAL MATCH p2 = (friend)-[:FRIEND_OF]-(other:Node)
WHERE other IN sample_friends
OPTIONAL MATCH p3 = (friend)-[:BELONGS_TO]->(circle:Circle)
RETURN p1, p2, p3;

// ========== 8. 随机网络样本 ==========
// 随机显示一些关系
MATCH p = ()-[r]->()
WHERE rand() < 0.1
RETURN p
LIMIT 30;

// ========== 9. 密集网络区域 ==========
// 找到连接最密集的区域
MATCH (friend:Node)-[:FRIEND_OF]-(other:Node)
WITH friend, count(other) as connections
ORDER BY connections DESC
LIMIT 1
MATCH p = (friend)-[:FRIEND_OF]-(connected:Node)
RETURN p
LIMIT 20;

// ========== 10. 多关系类型显示 ==========
// 同时显示多种关系类型
MATCH p1 = ()-[:HAS_FRIEND]->()
WITH p1 LIMIT 10
MATCH p2 = ()-[:FRIEND_OF]-()
WITH p1, p2 LIMIT 10
MATCH p3 = ()-[:BELONGS_TO]->()
RETURN p1, p2, p3
LIMIT 10;
