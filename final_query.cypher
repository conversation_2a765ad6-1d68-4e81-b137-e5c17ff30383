// 最终的Twitter网络查询 - 包含Circle数据，用于Sigma.js可视化

MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
WHERE friend_count > 8 AND friend_count < 20
ORDER BY friend_count DESC
LIMIT 20

MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
WITH ego, collect(friend)[0..20] as sample_friends
UNWIND sample_friends as friend
OPTIONAL MATCH (friend)-[rel:FRIEND_OF]-(other:Node)
WHERE other IN sample_friends
OPTIONAL MATCH (friend)-[:BELONGS_TO]->(circle:Circle)

WITH ego, friend, other, rel, circle,
     collect(DISTINCT {
       id: ego.id,
       label: 'EGO',
       dataset: ego.dataset,
       type: 'ego',
       features: ego.active_features[0..3]
     }) as ego_nodes,
     collect(DISTINCT {
       id: friend.id,
       label: 'Friend',
       dataset: friend.dataset,
       type: 'friend',
       features: friend.active_features[0..2]
     }) as friend_nodes,
     collect(DISTINCT {
       id: other.id,
       label: 'Friend',
       dataset: other.dataset,
       type: 'friend',
       features: other.active_features[0..2]
     }) as other_nodes,
     collect(DISTINCT {
       id: 'circle_' + circle.dataset + '_' + circle.circle_id,
       label: 'Circle ' + circle.circle_id,
       dataset: circle.dataset,
       type: 'circle',
       circle_id: circle.circle_id,
       size: circle.size
     }) as circle_nodes

RETURN {
  nodes: ego_nodes + friend_nodes + other_nodes + circle_nodes,
  edges: [
    {
      source: ego.id,
      target: friend.id,
      type: 'HAS_FRIEND',
      dataset: ego.dataset
    }
  ] + CASE WHEN other IS NOT NULL THEN [
    {
      source: friend.id,
      target: other.id,
      type: 'FRIEND_OF',
      dataset: friend.dataset
    }
  ] ELSE [] END + CASE WHEN circle IS NOT NULL THEN [
    {
      source: friend.id,
      target: 'circle_' + circle.dataset + '_' + circle.circle_id,
      type: 'BELONGS_TO',
      dataset: circle.dataset
    }
  ] ELSE [] END
} as graph_data;
