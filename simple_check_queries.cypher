// 简单检查查询 - 逐步验证数据是否存在

// ========== 1. 最基础检查 - 看看有没有数据 ==========
MATCH (n) RETURN n LIMIT 10;

// ========== 2. 检查节点类型 ==========
MATCH (n) RETURN labels(n), count(n);

// ========== 3. 检查EGO节点 ==========
MATCH (ego:EgoNode) RETURN ego LIMIT 5;

// ========== 4. 检查朋友节点 ==========
MATCH (friend:Node) RETURN friend LIMIT 5;

// ========== 5. 检查关系 ==========
MATCH ()-[r]->() RETURN type(r), count(r);

// ========== 6. 最简单的关系查询 ==========
MATCH (ego:EgoNode)-[r:HAS_FRIEND]->(friend:Node) 
RETURN ego, r, friend 
LIMIT 5;

// ========== 7. 检查特定数据集 ==========
MATCH (ego:EgoNode {dataset: '12831'}) RETURN ego;

// ========== 8. 检查是否有朋友关系 ==========
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node) 
RETURN ego.dataset, count(friend) as friends_count 
ORDER BY friends_count DESC 
LIMIT 10;

// ========== 9. 最简单的图形查询 ==========
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node) 
RETURN ego, friend 
LIMIT 10;

// ========== 10. 检查数据是否导入成功 ==========
MATCH (ego:EgoNode) 
WITH ego.dataset as dataset, count(ego) as ego_count
MATCH (friend:Node {dataset: dataset})
RETURN dataset, ego_count, count(friend) as friend_count
LIMIT 5;
