// 一体化查询 - 展示完整的Twitter网络数据

// 自动选择一个合适的用户并展示其完整网络
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
WHERE friend_count >= 15 AND friend_count <= 40
ORDER BY friend_count DESC
LIMIT 1

// 展示EGO用户详情
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
OPTIONAL MATCH (friend)-[rel:FRIEND_OF]-(other_friend:Node)
WHERE other_friend.dataset = ego.dataset
OPTIONAL MATCH (friend)-[:BELONGS_TO]->(circle:Circle)

// 汇总所有数据
WITH ego,
     collect(DISTINCT friend) as all_friends,
     collect(DISTINCT {f1: friend, f2: other_friend}) as all_relationships,
     collect(DISTINCT {circle: circle, member: friend}) as circle_memberships

RETURN
  // 1. EGO用户信息
  ego.dataset as ego_dataset,
  ego.id as ego_id,
  size(ego.active_features) as ego_active_features_count,
  ego.active_features[0..8] as ego_sample_features,

  // 2. 朋友节点信息
  size(all_friends) as total_friends_count,
  [f IN all_friends[0..10] | {
    id: f.id,
    features_count: size(f.active_features),
    sample_features: f.active_features[0..3]
  }] as friends_sample,

  // 3. 朋友关系信息
  size([r IN all_relationships WHERE r.f2 IS NOT NULL]) as total_friendships,
  [r IN all_relationships WHERE r.f2 IS NOT NULL | {
    friend1: r.f1.id,
    friend2: r.f2.id
  }][0..8] as friendships_sample,

  // 4. 社交圈子信息
  size([c IN circle_memberships WHERE c.circle IS NOT NULL | c.circle]) as total_circles,
  [c IN circle_memberships WHERE c.circle IS NOT NULL | {
    circle_id: c.circle.circle_id,
    size: c.circle.size,
    member_id: c.member.id
  }][0..10] as circles_sample
