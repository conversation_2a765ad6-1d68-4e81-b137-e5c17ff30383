// 一体化查询 - 展示完整的Twitter网络数据

// 自动选择一个合适的用户并展示其完整网络
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
WHERE friend_count BETWEEN 15 AND 40
ORDER BY friend_count DESC
LIMIT 1

// 展示EGO用户详情
WITH ego
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
OPTIONAL MATCH (friend)-[rel:FRIEND_OF]-(other_friend:Node {dataset: ego.dataset})
OPTIONAL MATCH (friend)-[:BELONGS_TO]->(circle:Circle)

// 汇总所有数据
WITH ego, 
     collect(DISTINCT friend) as all_friends,
     collect(DISTINCT {f1: friend, f2: other_friend, rel: rel}) as all_relationships,
     collect(DISTINCT {circle: circle, member: friend}) as circle_memberships

RETURN 
  // 1. EGO用户信息
  "=== EGO USER ===" as section1,
  ego.dataset as ego_dataset,
  ego.id as ego_id,
  size(ego.active_features) as ego_active_features_count,
  ego.active_features[0..8] as ego_sample_features,
  
  // 2. 朋友节点信息
  "=== FRIENDS ===" as section2,
  size(all_friends) as total_friends_count,
  [f IN all_friends[0..10] | {
    id: f.id, 
    features_count: size(f.active_features),
    sample_features: f.active_features[0..3]
  }] as friends_sample,
  
  // 3. 朋友关系信息
  "=== FRIENDSHIPS ===" as section3,
  size([r IN all_relationships WHERE r.f2 IS NOT NULL]) as total_friendships,
  [r IN all_relationships WHERE r.f2 IS NOT NULL][0..8] as friendships_sample,
  
  // 4. 社交圈子信息
  "=== CIRCLES ===" as section4,
  size(collect(DISTINCT circle_memberships[0].circle)) as total_circles,
  [c IN collect(DISTINCT {
    circle_id: circle_memberships[0].circle.circle_id,
    size: circle_memberships[0].circle.size,
    members: [m IN circle_memberships WHERE m.circle.circle_id = circle_memberships[0].circle.circle_id | m.member.id][0..5]
  })] as circles_info

UNION ALL

// 展示特征名称样本
MATCH (feat:FeatureName)
WITH feat.dataset as dataset, collect(feat) as features
WHERE size(features) > 0
WITH dataset, features[0..15] as sample_features
RETURN 
  "=== FEATURES ===" as section1,
  dataset as ego_dataset,
  null as ego_id,
  null as ego_active_features_count,
  null as ego_sample_features,
  
  "=== FEATURE NAMES ===" as section2,
  size(sample_features) as total_friends_count,
  [f IN sample_features | {
    index: f.index,
    name: f.name
  }] as friends_sample,
  
  null as section3,
  null as total_friendships,
  null as friendships_sample,
  
  null as section4,
  null as total_circles,
  null as circles_info
LIMIT 1;
