// 综合查询 - 一次性展示所有关键节点数据

// 选择一个有适量朋友的用户进行完整展示
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
WHERE friend_count >= 10 AND friend_count <= 50  // 选择朋友数适中的用户
ORDER BY friend_count DESC
LIMIT 1

// 获取该用户的完整网络数据
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
OPTIONAL MATCH (friend)-[friendship:FRIEND_OF]-(other_friend:Node)
WHERE other_friend.dataset = ego.dataset
OPTIONAL MATCH (friend)-[:BELONGS_TO]->(circle:Circle)
OPTIONAL MATCH (feat:FeatureName {dataset: ego.dataset})
WHERE feat.name IN ego.active_features

// 返回完整的网络结构和节点详情
RETURN 
  // === EGO用户信息 ===
  {
    type: 'EGO_USER',
    id: ego.id,
    dataset: ego.dataset,
    total_features: ego.feature_count,
    active_features_count: size(ego.active_features),
    active_features: ego.active_features[0..10],
    feature_vector_sample: ego.features[0..20]
  } as ego_data,
  
  // === 朋友节点信息 ===
  collect(DISTINCT {
    type: 'FRIEND',
    id: friend.id,
    active_features_count: size(friend.active_features),
    active_features: friend.active_features[0..5],
    feature_vector_sample: friend.features[0..10]
  }) as friends_data,
  
  // === 朋友关系信息 ===
  collect(DISTINCT {
    type: 'FRIENDSHIP',
    friend1_id: friend.id,
    friend2_id: other_friend.id,
    relationship: 'FRIEND_OF'
  }) as friendships_data,
  
  // === 社交圈子信息 ===
  collect(DISTINCT {
    type: 'CIRCLE',
    circle_id: circle.circle_id,
    size: circle.size,
    member_id: friend.id
  }) as circles_data,
  
  // === 特征名称信息 ===
  collect(DISTINCT {
    type: 'FEATURE',
    index: feat.index,
    name: feat.name
  }) as features_data

LIMIT 1;
