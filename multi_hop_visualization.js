// 多跳查询可视化 - 专门用于多跳网络的Sigma.js集成

// 1. 多跳查询API函数
async function fetchMultiHopNetwork(nodeId, maxHops = 3) {
    // 使用结构化返回格式的多跳查询
    const query = `
        MATCH (start {id: $nodeId})

        // 第1跳：获取直接连接的节点
        MATCH (start)-[r1]-(hop1)
        WITH start, collect(DISTINCT hop1)[0..10] as first_hop_nodes, collect(DISTINCT r1) as first_hop_rels

        // 第2跳：获取第二层节点（如果maxHops >= 2）
        UNWIND first_hop_nodes as hop1_node
        OPTIONAL MATCH (hop1_node)-[r2]-(hop2)
        WHERE $maxHops >= 2 AND hop2 <> start AND NOT hop2 IN first_hop_nodes
        WITH start, first_hop_nodes, first_hop_rels,
             collect(DISTINCT hop2)[0..15] as second_hop_nodes,
             collect(DISTINCT r2) as second_hop_rels

        // 第3跳：获取第三层节点（如果maxHops >= 3）
        UNWIND (CASE WHEN $maxHops >= 3 THEN second_hop_nodes ELSE [] END) as hop2_node
        OPTIONAL MATCH (hop2_node)-[r3]-(hop3)
        WHERE $maxHops >= 3 AND hop3 <> start AND NOT hop3 IN first_hop_nodes AND NOT hop3 IN second_hop_nodes
        WITH start, first_hop_nodes, first_hop_rels, second_hop_nodes, second_hop_rels,
             collect(DISTINCT hop3)[0..10] as third_hop_nodes,
             collect(DISTINCT r3) as third_hop_rels

        RETURN {
          nodes: [{
            id: CASE
              WHEN 'EgoNode' IN labels(start) THEN start.id
              WHEN 'Node' IN labels(start) THEN start.id
              WHEN 'Circle' IN labels(start) THEN 'circle_' + start.dataset + '_' + start.circle_id
              ELSE start.id
            END,
            label: CASE
              WHEN 'EgoNode' IN labels(start) THEN 'EGO_' + start.dataset
              WHEN 'Node' IN labels(start) THEN 'Friend_' + start.id
              WHEN 'Circle' IN labels(start) THEN 'Circle_' + start.circle_id
              ELSE toString(start.id)
            END,
            type: CASE
              WHEN 'EgoNode' IN labels(start) THEN 'ego'
              WHEN 'Node' IN labels(start) THEN 'friend'
              WHEN 'Circle' IN labels(start) THEN 'circle'
              ELSE 'unknown'
            END,
            dataset: start.dataset,
            features: CASE
              WHEN 'EgoNode' IN labels(start) OR 'Node' IN labels(start)
              THEN start.active_features[0..3]
              ELSE []
            END,
            hop: 0
          }] +
          [n IN first_hop_nodes | {
            id: CASE
              WHEN 'EgoNode' IN labels(n) THEN n.id
              WHEN 'Node' IN labels(n) THEN n.id
              WHEN 'Circle' IN labels(n) THEN 'circle_' + n.dataset + '_' + n.circle_id
              ELSE n.id
            END,
            label: CASE
              WHEN 'EgoNode' IN labels(n) THEN 'EGO_' + n.dataset
              WHEN 'Node' IN labels(n) THEN 'Friend_' + n.id
              WHEN 'Circle' IN labels(n) THEN 'Circle_' + n.circle_id
              ELSE toString(n.id)
            END,
            type: CASE
              WHEN 'EgoNode' IN labels(n) THEN 'ego'
              WHEN 'Node' IN labels(n) THEN 'friend'
              WHEN 'Circle' IN labels(n) THEN 'circle'
              ELSE 'unknown'
            END,
            dataset: n.dataset,
            features: CASE
              WHEN 'EgoNode' IN labels(n) OR 'Node' IN labels(n)
              THEN n.active_features[0..2]
              ELSE []
            END,
            hop: 1
          }] +
          [n IN second_hop_nodes | {
            id: CASE
              WHEN 'EgoNode' IN labels(n) THEN n.id
              WHEN 'Node' IN labels(n) THEN n.id
              WHEN 'Circle' IN labels(n) THEN 'circle_' + n.dataset + '_' + n.circle_id
              ELSE n.id
            END,
            label: CASE
              WHEN 'EgoNode' IN labels(n) THEN 'EGO_' + n.dataset
              WHEN 'Node' IN labels(n) THEN 'Friend_' + n.id
              WHEN 'Circle' IN labels(n) THEN 'Circle_' + n.circle_id
              ELSE toString(n.id)
            END,
            type: CASE
              WHEN 'EgoNode' IN labels(n) THEN 'ego'
              WHEN 'Node' IN labels(n) THEN 'friend'
              WHEN 'Circle' IN labels(n) THEN 'circle'
              ELSE 'unknown'
            END,
            dataset: n.dataset,
            features: CASE
              WHEN 'EgoNode' IN labels(n) OR 'Node' IN labels(n)
              THEN n.active_features[0..2]
              ELSE []
            END,
            hop: 2
          }] +
          [n IN third_hop_nodes | {
            id: CASE
              WHEN 'EgoNode' IN labels(n) THEN n.id
              WHEN 'Node' IN labels(n) THEN n.id
              WHEN 'Circle' IN labels(n) THEN 'circle_' + n.dataset + '_' + n.circle_id
              ELSE n.id
            END,
            label: CASE
              WHEN 'EgoNode' IN labels(n) THEN 'EGO_' + n.dataset
              WHEN 'Node' IN labels(n) THEN 'Friend_' + n.id
              WHEN 'Circle' IN labels(n) THEN 'Circle_' + n.circle_id
              ELSE toString(n.id)
            END,
            type: CASE
              WHEN 'EgoNode' IN labels(n) THEN 'ego'
              WHEN 'Node' IN labels(n) THEN 'friend'
              WHEN 'Circle' IN labels(n) THEN 'circle'
              ELSE 'unknown'
            END,
            dataset: n.dataset,
            features: CASE
              WHEN 'EgoNode' IN labels(n) OR 'Node' IN labels(n)
              THEN n.active_features[0..2]
              ELSE []
            END,
            hop: 3
          }],

          edges: [rel IN first_hop_rels | {
            source: CASE
              WHEN 'Circle' IN labels(startNode(rel))
              THEN 'circle_' + startNode(rel).dataset + '_' + startNode(rel).circle_id
              ELSE startNode(rel).id
            END,
            target: CASE
              WHEN 'Circle' IN labels(endNode(rel))
              THEN 'circle_' + endNode(rel).dataset + '_' + endNode(rel).circle_id
              ELSE endNode(rel).id
            END,
            type: type(rel),
            dataset: startNode(rel).dataset
          }] +
          [rel IN second_hop_rels | {
            source: CASE
              WHEN 'Circle' IN labels(startNode(rel))
              THEN 'circle_' + startNode(rel).dataset + '_' + startNode(rel).circle_id
              ELSE startNode(rel).id
            END,
            target: CASE
              WHEN 'Circle' IN labels(endNode(rel))
              THEN 'circle_' + endNode(rel).dataset + '_' + endNode(rel).circle_id
              ELSE endNode(rel).id
            END,
            type: type(rel),
            dataset: startNode(rel).dataset
          }] +
          [rel IN third_hop_rels | {
            source: CASE
              WHEN 'Circle' IN labels(startNode(rel))
              THEN 'circle_' + startNode(rel).dataset + '_' + startNode(rel).circle_id
              ELSE startNode(rel).id
            END,
            target: CASE
              WHEN 'Circle' IN labels(endNode(rel))
              THEN 'circle_' + endNode(rel).dataset + '_' + endNode(rel).circle_id
              ELSE endNode(rel).id
            END,
            type: type(rel),
            dataset: startNode(rel).dataset
          }]
        } as multi_hop_network;
    `;

    const parameters = { nodeId: parseInt(nodeId), maxHops: maxHops };
    
    // 模拟API调用，实际使用时替换为真实的Neo4j API
    const response = await fetch('/api/neo4j/multi-hop', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query, parameters })
    });
    
    return await response.json();
}

// 2. 转换多跳数据为Sigma格式
function convertMultiHopToSigma(multiHopData) {
    const sigmaData = {
        nodes: [],
        edges: []
    };

    // 处理结构化的多跳数据
    multiHopData.forEach(record => {
        const networkData = record.multi_hop_network;

        // 转换节点
        networkData.nodes.forEach(node => {
            if (!sigmaData.nodes.find(n => n.key === String(node.id))) {
                // 根据跳数设置颜色和大小
                let color, size;
                switch(node.hop) {
                    case 0: // 起始节点
                        color = '#e74c3c'; // 红色
                        size = 20;
                        break;
                    case 1: // 第1跳
                        color = '#3498db'; // 蓝色
                        size = 15;
                        break;
                    case 2: // 第2跳
                        color = '#2ecc71'; // 绿色
                        size = 12;
                        break;
                    case 3: // 第3跳
                        color = '#9b59b6'; // 紫色
                        size = 10;
                        break;
                    default:
                        color = '#95a5a6'; // 灰色
                        size = 8;
                }

                sigmaData.nodes.push({
                    key: String(node.id),
                    label: node.label || String(node.id),
                    tag: node.type,
                    dataset: node.dataset,
                    features: node.features || [],
                    hop: node.hop,
                    x: Math.random() * 1000,
                    y: Math.random() * 1000,
                    size: size,
                    color: color
                });
            }
        });

        // 转换边
        networkData.edges.forEach((edge, edgeIndex) => {
            if (edge.source && edge.target) {
                sigmaData.edges.push({
                    key: `edge_${edgeIndex}_${edge.source}_${edge.target}`,
                    source: String(edge.source),
                    target: String(edge.target),
                    label: edge.type,
                    type: edge.type,
                    dataset: edge.dataset,
                    color: getMultiHopEdgeColor(edge.type),
                    size: getMultiHopEdgeSize(edge.type)
                });
            }
        });
    });

    return sigmaData;
}

// 3. 获取多跳边的颜色
function getMultiHopEdgeColor(edgeType) {
    switch(edgeType) {
        case 'HAS_FRIEND': return '#2980b9';
        case 'FRIEND_OF': return '#27ae60';
        case 'BELONGS_TO': return '#8e44ad';
        default: return '#95a5a6';
    }
}

// 4. 获取多跳边的大小
function getMultiHopEdgeSize(edgeType) {
    switch(edgeType) {
        case 'HAS_FRIEND': return 3;
        case 'FRIEND_OF': return 2;
        case 'BELONGS_TO': return 2;
        default: return 1;
    }
}

// 5. 渲染多跳网络
function renderMultiHopNetwork(container, data, startNodeInfo) {
    // 清除现有图形
    if (window.multiHopSigma) {
        window.multiHopSigma.kill();
    }
    
    // 创建Sigma实例
    window.multiHopSigma = new Sigma({
        graph: data,
        container: container,
        settings: {
            defaultNodeColor: '#95a5a6',
            defaultEdgeColor: '#bdc3c7',
            nodesPowRatio: 0.8,
            edgesPowRatio: 0.8,
            drawLabels: true,
            labelThreshold: 6,
            labelSize: 'proportional',
            labelSizeRatio: 1.5,
            mouseWheelEnabled: true,
            doubleClickEnabled: true,
            minNodeSize: 8,
            maxNodeSize: 25,
            minEdgeSize: 1,
            maxEdgeSize: 5,
            animationsTime: 1500
        }
    });
    
    // 添加事件监听
    window.multiHopSigma.bind('clickNode', function(e) {
        const node = e.data.node;
        showMultiHopNodeDetails(node, startNodeInfo);
        highlightMultiHopNeighbors(node);
    });
    
    // 启动布局
    startMultiHopLayout();
    
    return window.multiHopSigma;
}

// 6. 显示多跳节点详情
function showMultiHopNodeDetails(node, startNodeInfo) {
    const detailsHtml = `
        <div class="multi-hop-details">
            <h3>多跳节点详情</h3>
            <p><strong>ID:</strong> ${node.label}</p>
            <p><strong>类型:</strong> ${node.tag}</p>
            <p><strong>数据集:</strong> ${node.dataset}</p>
            ${node.features && node.features.length > 0 ? 
                `<p><strong>特征:</strong> ${node.features.slice(0, 3).join(', ')}</p>` : ''}
            ${node.circle_info ? 
                `<p><strong>圈子ID:</strong> ${node.circle_info.circle_id}</p>
                 <p><strong>圈子大小:</strong> ${node.circle_info.size}</p>` : ''}
            <hr>
            <p><strong>起始节点:</strong> ${startNodeInfo.type} - ${startNodeInfo.id}</p>
        </div>
    `;
    
    document.getElementById('multi-hop-details').innerHTML = detailsHtml;
}

// 7. 高亮多跳邻居节点
function highlightMultiHopNeighbors(centerNode) {
    if (!window.multiHopSigma) return;
    
    const nodeId = centerNode.key;
    const neighbors = [];
    
    // 找到所有邻居
    window.multiHopSigma.graph.edges().forEach(edge => {
        if (edge.source === nodeId) {
            neighbors.push(edge.target);
        } else if (edge.target === nodeId) {
            neighbors.push(edge.source);
        }
    });
    
    // 重置所有节点颜色
    window.multiHopSigma.graph.nodes().forEach(node => {
        if (node.key === nodeId) {
            node.color = '#e74c3c'; // 中心节点红色
        } else if (neighbors.includes(node.key)) {
            node.color = '#f39c12'; // 邻居节点橙色
        } else {
            // 恢复原始颜色
            switch(node.tag) {
                case 'ego': node.color = '#e74c3c'; break;
                case 'friend': node.color = '#3498db'; break;
                case 'circle': node.color = '#9b59b6'; break;
                default: node.color = '#95a5a6';
            }
        }
    });
    
    window.multiHopSigma.refresh();
}

// 8. 启动多跳布局
function startMultiHopLayout() {
    if (window.multiHopSigma) {
        window.multiHopSigma.startForceAtlas2({
            worker: true,
            barnesHutOptimize: true,
            barnesHutTheta: 0.5,
            iterationsPerRender: 1,
            linLogMode: false,
            outboundAttractionDistribution: false,
            adjustSizes: false,
            edgeWeightInfluence: 1,
            scalingRatio: 1,
            strongGravityMode: false,
            gravity: 1
        });
        
        // 15秒后停止布局
        setTimeout(() => {
            if (window.multiHopSigma) {
                window.multiHopSigma.stopForceAtlas2();
            }
        }, 15000);
    }
}

// 9. 主初始化函数
async function initMultiHopVisualization(nodeId, maxHops = 3) {
    try {
        console.log(`正在获取节点${nodeId}的${maxHops}跳网络数据...`);
        showLoading(true);

        const multiHopData = await fetchMultiHopNetwork(nodeId, maxHops);
        const sigmaData = convertMultiHopToSigma(multiHopData);

        console.log('多跳数据:', sigmaData);
        console.log(`节点数: ${sigmaData.nodes.length}, 边数: ${sigmaData.edges.length}`);

        const container = document.getElementById('multi-hop-container');
        const startNodeInfo = { id: nodeId };
        renderMultiHopNetwork(container, sigmaData, startNodeInfo);

        // 更新统计信息
        updateMultiHopStats(sigmaData, startNodeInfo, maxHops);

        console.log('多跳网络可视化完成！');

    } catch (error) {
        console.error('多跳网络初始化失败:', error);
    } finally {
        showLoading(false);
    }
}

// 10. 更新多跳统计信息
function updateMultiHopStats(data, startNodeInfo, maxHops) {
    const nodeTypeCounts = {};
    data.nodes.forEach(node => {
        const type = node.tag || 'unknown';
        nodeTypeCounts[type] = (nodeTypeCounts[type] || 0) + 1;
    });
    
    let statsHtml = `
        <h3>多跳网络统计</h3>
        <p><strong>起始节点:</strong> ${startNodeInfo.type} - ${startNodeInfo.id}</p>
        <p><strong>最大跳数:</strong> ${maxHops}</p>
        <p><strong>总节点数:</strong> ${data.nodes.length}</p>
        <p><strong>总边数:</strong> ${data.edges.length}</p>
        <hr>
    `;
    
    Object.keys(nodeTypeCounts).forEach(type => {
        statsHtml += `<p>${type}节点: ${nodeTypeCounts[type]} 个</p>`;
    });
    
    document.getElementById('multi-hop-stats').innerHTML = statsHtml;
}

// 导出函数供HTML使用
window.initMultiHopVisualization = initMultiHopVisualization;
window.fetchMultiHopNetwork = fetchMultiHopNetwork;
