// 多跳查询可视化 - 专门用于多跳网络的Sigma.js集成

// 1. 多跳查询API函数
async function fetchMultiHopNetwork(nodeId, maxHops = 3) {
    // 简化的多跳查询，不区分节点类型
    const query = `
        MATCH p=(n {id: $nodeId})-[*1..$maxHops]-(m)
        RETURN p
        LIMIT 100
    `;

    const parameters = { nodeId: parseInt(nodeId), maxHops: maxHops };
    
    // 模拟API调用，实际使用时替换为真实的Neo4j API
    const response = await fetch('/api/neo4j/multi-hop', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query, parameters })
    });
    
    return await response.json();
}

// 2. 转换多跳数据为Sigma格式
function convertMultiHopToSigma(multiHopData) {
    const sigmaData = {
        nodes: [],
        edges: []
    };

    // 处理路径数据
    multiHopData.forEach(record => {
        const path = record.p;

        // 提取路径中的节点
        path.nodes.forEach(node => {
            if (!sigmaData.nodes.find(n => n.key === String(node.id))) {
                // 根据节点标签设置样式
                let color, size, type;
                if (node.labels.includes('EgoNode')) {
                    color = '#e74c3c'; // 红色
                    size = 20;
                    type = 'ego';
                } else if (node.labels.includes('Node')) {
                    color = '#3498db'; // 蓝色
                    size = 12;
                    type = 'friend';
                } else if (node.labels.includes('Circle')) {
                    color = '#9b59b6'; // 紫色
                    size = 15;
                    type = 'circle';
                } else {
                    color = '#95a5a6'; // 灰色
                    size = 8;
                    type = 'unknown';
                }

                sigmaData.nodes.push({
                    key: String(node.id),
                    label: String(node.id),
                    tag: type,
                    dataset: node.properties.dataset,
                    features: node.properties.active_features || [],
                    x: Math.random() * 1000,
                    y: Math.random() * 1000,
                    size: size,
                    color: color
                });
            }
        });

        // 提取路径中的关系
        path.relationships.forEach((rel, relIndex) => {
            sigmaData.edges.push({
                key: `edge_${relIndex}_${rel.start}_${rel.end}`,
                source: String(rel.start),
                target: String(rel.end),
                label: rel.type,
                type: rel.type,
                color: getMultiHopEdgeColor(rel.type),
                size: getMultiHopEdgeSize(rel.type)
            });
        });
    });

    return sigmaData;
}

// 3. 获取多跳边的颜色
function getMultiHopEdgeColor(edgeType) {
    switch(edgeType) {
        case 'HAS_FRIEND': return '#2980b9';
        case 'FRIEND_OF': return '#27ae60';
        case 'BELONGS_TO': return '#8e44ad';
        default: return '#95a5a6';
    }
}

// 4. 获取多跳边的大小
function getMultiHopEdgeSize(edgeType) {
    switch(edgeType) {
        case 'HAS_FRIEND': return 3;
        case 'FRIEND_OF': return 2;
        case 'BELONGS_TO': return 2;
        default: return 1;
    }
}

// 5. 渲染多跳网络
function renderMultiHopNetwork(container, data, startNodeInfo) {
    // 清除现有图形
    if (window.multiHopSigma) {
        window.multiHopSigma.kill();
    }
    
    // 创建Sigma实例
    window.multiHopSigma = new Sigma({
        graph: data,
        container: container,
        settings: {
            defaultNodeColor: '#95a5a6',
            defaultEdgeColor: '#bdc3c7',
            nodesPowRatio: 0.8,
            edgesPowRatio: 0.8,
            drawLabels: true,
            labelThreshold: 6,
            labelSize: 'proportional',
            labelSizeRatio: 1.5,
            mouseWheelEnabled: true,
            doubleClickEnabled: true,
            minNodeSize: 8,
            maxNodeSize: 25,
            minEdgeSize: 1,
            maxEdgeSize: 5,
            animationsTime: 1500
        }
    });
    
    // 添加事件监听
    window.multiHopSigma.bind('clickNode', function(e) {
        const node = e.data.node;
        showMultiHopNodeDetails(node, startNodeInfo);
        highlightMultiHopNeighbors(node);
    });
    
    // 启动布局
    startMultiHopLayout();
    
    return window.multiHopSigma;
}

// 6. 显示多跳节点详情
function showMultiHopNodeDetails(node, startNodeInfo) {
    const detailsHtml = `
        <div class="multi-hop-details">
            <h3>多跳节点详情</h3>
            <p><strong>ID:</strong> ${node.label}</p>
            <p><strong>类型:</strong> ${node.tag}</p>
            <p><strong>数据集:</strong> ${node.dataset}</p>
            ${node.features && node.features.length > 0 ? 
                `<p><strong>特征:</strong> ${node.features.slice(0, 3).join(', ')}</p>` : ''}
            ${node.circle_info ? 
                `<p><strong>圈子ID:</strong> ${node.circle_info.circle_id}</p>
                 <p><strong>圈子大小:</strong> ${node.circle_info.size}</p>` : ''}
            <hr>
            <p><strong>起始节点:</strong> ${startNodeInfo.type} - ${startNodeInfo.id}</p>
        </div>
    `;
    
    document.getElementById('multi-hop-details').innerHTML = detailsHtml;
}

// 7. 高亮多跳邻居节点
function highlightMultiHopNeighbors(centerNode) {
    if (!window.multiHopSigma) return;
    
    const nodeId = centerNode.key;
    const neighbors = [];
    
    // 找到所有邻居
    window.multiHopSigma.graph.edges().forEach(edge => {
        if (edge.source === nodeId) {
            neighbors.push(edge.target);
        } else if (edge.target === nodeId) {
            neighbors.push(edge.source);
        }
    });
    
    // 重置所有节点颜色
    window.multiHopSigma.graph.nodes().forEach(node => {
        if (node.key === nodeId) {
            node.color = '#e74c3c'; // 中心节点红色
        } else if (neighbors.includes(node.key)) {
            node.color = '#f39c12'; // 邻居节点橙色
        } else {
            // 恢复原始颜色
            switch(node.tag) {
                case 'ego': node.color = '#e74c3c'; break;
                case 'friend': node.color = '#3498db'; break;
                case 'circle': node.color = '#9b59b6'; break;
                default: node.color = '#95a5a6';
            }
        }
    });
    
    window.multiHopSigma.refresh();
}

// 8. 启动多跳布局
function startMultiHopLayout() {
    if (window.multiHopSigma) {
        window.multiHopSigma.startForceAtlas2({
            worker: true,
            barnesHutOptimize: true,
            barnesHutTheta: 0.5,
            iterationsPerRender: 1,
            linLogMode: false,
            outboundAttractionDistribution: false,
            adjustSizes: false,
            edgeWeightInfluence: 1,
            scalingRatio: 1,
            strongGravityMode: false,
            gravity: 1
        });
        
        // 15秒后停止布局
        setTimeout(() => {
            if (window.multiHopSigma) {
                window.multiHopSigma.stopForceAtlas2();
            }
        }, 15000);
    }
}

// 9. 主初始化函数
async function initMultiHopVisualization(nodeId, maxHops = 3) {
    try {
        console.log(`正在获取节点${nodeId}的${maxHops}跳网络数据...`);
        showLoading(true);

        const multiHopData = await fetchMultiHopNetwork(nodeId, maxHops);
        const sigmaData = convertMultiHopToSigma(multiHopData);

        console.log('多跳数据:', sigmaData);
        console.log(`节点数: ${sigmaData.nodes.length}, 边数: ${sigmaData.edges.length}`);

        const container = document.getElementById('multi-hop-container');
        const startNodeInfo = { id: nodeId };
        renderMultiHopNetwork(container, sigmaData, startNodeInfo);

        // 更新统计信息
        updateMultiHopStats(sigmaData, startNodeInfo, maxHops);

        console.log('多跳网络可视化完成！');

    } catch (error) {
        console.error('多跳网络初始化失败:', error);
    } finally {
        showLoading(false);
    }
}

// 10. 更新多跳统计信息
function updateMultiHopStats(data, startNodeInfo, maxHops) {
    const nodeTypeCounts = {};
    data.nodes.forEach(node => {
        const type = node.tag || 'unknown';
        nodeTypeCounts[type] = (nodeTypeCounts[type] || 0) + 1;
    });
    
    let statsHtml = `
        <h3>多跳网络统计</h3>
        <p><strong>起始节点:</strong> ${startNodeInfo.type} - ${startNodeInfo.id}</p>
        <p><strong>最大跳数:</strong> ${maxHops}</p>
        <p><strong>总节点数:</strong> ${data.nodes.length}</p>
        <p><strong>总边数:</strong> ${data.edges.length}</p>
        <hr>
    `;
    
    Object.keys(nodeTypeCounts).forEach(type => {
        statsHtml += `<p>${type}节点: ${nodeTypeCounts[type]} 个</p>`;
    });
    
    document.getElementById('multi-hop-stats').innerHTML = statsHtml;
}

// 导出函数供HTML使用
window.initMultiHopVisualization = initMultiHopVisualization;
window.fetchMultiHopNetwork = fetchMultiHopNetwork;
