// 多跳查询可视化 - 专门用于多跳网络的Sigma.js集成

// 1. 多跳查询API函数
async function fetchMultiHopNetwork(nodeType, nodeId, dataset, maxHops = 3) {
    // 根据节点类型构建不同的查询
    let query = '';
    let parameters = {};
    
    if (nodeType === 'ego') {
        query = `
            WITH $nodeId as ego_id
            MATCH (start:EgoNode {id: ego_id})
            MATCH path = (start)-[*1..$maxHops]-(end)
            WHERE length(path) <= $maxHops
            
            WITH ego_id, collect(DISTINCT nodes(path)) as all_path_nodes, 
                 collect(DISTINCT relationships(path)) as all_path_rels
            
            UNWIND all_path_nodes as node_list
            UNWIND node_list as node
            WITH ego_id, collect(DISTINCT node) as unique_nodes, all_path_rels
            
            UNWIND all_path_rels as rel_list
            UNWIND rel_list as rel
            
            RETURN {
              query_type: 'ego_multi_hop',
              start_ego: ego_id,
              nodes: [n IN unique_nodes | {
                id: CASE 
                  WHEN 'EgoNode' IN labels(n) THEN n.id
                  WHEN 'Node' IN labels(n) THEN n.id
                  WHEN 'Circle' IN labels(n) THEN 'circle_' + n.dataset + '_' + n.circle_id
                END,
                label: CASE 
                  WHEN 'EgoNode' IN labels(n) THEN 'EGO_' + n.dataset
                  WHEN 'Node' IN labels(n) THEN 'Friend_' + n.id
                  WHEN 'Circle' IN labels(n) THEN 'Circle_' + n.circle_id
                END,
                type: CASE 
                  WHEN 'EgoNode' IN labels(n) THEN 'ego'
                  WHEN 'Node' IN labels(n) THEN 'friend'
                  WHEN 'Circle' IN labels(n) THEN 'circle'
                END,
                dataset: n.dataset,
                features: CASE 
                  WHEN 'EgoNode' IN labels(n) OR 'Node' IN labels(n) 
                  THEN n.active_features[0..3]
                  ELSE []
                END
              }],
              edges: collect(DISTINCT {
                source: CASE 
                  WHEN 'Circle' IN labels(startNode(rel)) 
                  THEN 'circle_' + startNode(rel).dataset + '_' + startNode(rel).circle_id
                  ELSE startNode(rel).id
                END,
                target: CASE 
                  WHEN 'Circle' IN labels(endNode(rel)) 
                  THEN 'circle_' + endNode(rel).dataset + '_' + endNode(rel).circle_id
                  ELSE endNode(rel).id
                END,
                type: type(rel),
                dataset: startNode(rel).dataset
              })
            } as ego_multi_hop_data;
        `;
        parameters = { nodeId: parseInt(nodeId), maxHops: maxHops };
        
    } else if (nodeType === 'friend') {
        query = `
            WITH $nodeId as friend_id, $dataset as dataset
            MATCH (start:Node {id: friend_id, dataset: dataset})
            MATCH path = (start)-[*1..$maxHops]-(end)
            WHERE length(path) <= $maxHops
            
            WITH friend_id, dataset, collect(DISTINCT nodes(path)) as all_path_nodes,
                 collect(DISTINCT relationships(path)) as all_path_rels
            
            UNWIND all_path_nodes as node_list
            UNWIND node_list as node
            WITH friend_id, dataset, collect(DISTINCT node) as unique_nodes, all_path_rels
            
            UNWIND all_path_rels as rel_list
            UNWIND rel_list as rel
            
            RETURN {
              query_type: 'friend_multi_hop',
              start_friend: friend_id,
              start_dataset: dataset,
              nodes: [n IN unique_nodes | {
                id: CASE 
                  WHEN 'EgoNode' IN labels(n) THEN n.id
                  WHEN 'Node' IN labels(n) THEN n.id
                  WHEN 'Circle' IN labels(n) THEN 'circle_' + n.dataset + '_' + n.circle_id
                END,
                label: CASE 
                  WHEN 'EgoNode' IN labels(n) THEN 'EGO_' + n.dataset
                  WHEN 'Node' IN labels(n) THEN 'Friend_' + n.id
                  WHEN 'Circle' IN labels(n) THEN 'Circle_' + n.circle_id
                END,
                type: CASE 
                  WHEN 'EgoNode' IN labels(n) THEN 'ego'
                  WHEN 'Node' IN labels(n) THEN 'friend'
                  WHEN 'Circle' IN labels(n) THEN 'circle'
                END,
                dataset: n.dataset,
                features: CASE 
                  WHEN 'EgoNode' IN labels(n) OR 'Node' IN labels(n) 
                  THEN n.active_features[0..3]
                  ELSE []
                END
              }],
              edges: collect(DISTINCT {
                source: CASE 
                  WHEN 'Circle' IN labels(startNode(rel)) 
                  THEN 'circle_' + startNode(rel).dataset + '_' + startNode(rel).circle_id
                  ELSE startNode(rel).id
                END,
                target: CASE 
                  WHEN 'Circle' IN labels(endNode(rel)) 
                  THEN 'circle_' + endNode(rel).dataset + '_' + endNode(rel).circle_id
                  ELSE endNode(rel).id
                END,
                type: type(rel),
                dataset: startNode(rel).dataset
              })
            } as friend_multi_hop_data;
        `;
        parameters = { nodeId: parseInt(nodeId), dataset: dataset, maxHops: maxHops };
        
    } else if (nodeType === 'circle') {
        query = `
            WITH $circleId as circle_id, $dataset as dataset
            MATCH (start:Circle {circle_id: circle_id, dataset: dataset})
            MATCH path = (start)-[*1..$maxHops]-(end)
            WHERE length(path) <= $maxHops
            
            WITH circle_id, dataset, collect(DISTINCT nodes(path)) as all_path_nodes,
                 collect(DISTINCT relationships(path)) as all_path_rels
            
            UNWIND all_path_nodes as node_list
            UNWIND node_list as node
            WITH circle_id, dataset, collect(DISTINCT node) as unique_nodes, all_path_rels
            
            UNWIND all_path_rels as rel_list
            UNWIND rel_list as rel
            
            RETURN {
              query_type: 'circle_multi_hop',
              start_circle: circle_id,
              start_dataset: dataset,
              nodes: [n IN unique_nodes | {
                id: CASE 
                  WHEN 'EgoNode' IN labels(n) THEN n.id
                  WHEN 'Node' IN labels(n) THEN n.id
                  WHEN 'Circle' IN labels(n) THEN 'circle_' + n.dataset + '_' + n.circle_id
                END,
                label: CASE 
                  WHEN 'EgoNode' IN labels(n) THEN 'EGO_' + n.dataset
                  WHEN 'Node' IN labels(n) THEN 'Friend_' + n.id
                  WHEN 'Circle' IN labels(n) THEN 'Circle_' + n.circle_id
                END,
                type: CASE 
                  WHEN 'EgoNode' IN labels(n) THEN 'ego'
                  WHEN 'Node' IN labels(n) THEN 'friend'
                  WHEN 'Circle' IN labels(n) THEN 'circle'
                END,
                dataset: n.dataset,
                features: CASE 
                  WHEN 'EgoNode' IN labels(n) OR 'Node' IN labels(n) 
                  THEN n.active_features[0..3]
                  ELSE []
                END,
                circle_info: CASE 
                  WHEN 'Circle' IN labels(n) 
                  THEN {circle_id: n.circle_id, size: n.size}
                  ELSE null
                END
              }],
              edges: collect(DISTINCT {
                source: CASE 
                  WHEN 'Circle' IN labels(startNode(rel)) 
                  THEN 'circle_' + startNode(rel).dataset + '_' + startNode(rel).circle_id
                  ELSE startNode(rel).id
                END,
                target: CASE 
                  WHEN 'Circle' IN labels(endNode(rel)) 
                  THEN 'circle_' + endNode(rel).dataset + '_' + endNode(rel).circle_id
                  ELSE endNode(rel).id
                END,
                type: type(rel),
                dataset: startNode(rel).dataset
              })
            } as circle_multi_hop_data;
        `;
        parameters = { circleId: parseInt(nodeId), dataset: dataset, maxHops: maxHops };
    }
    
    // 模拟API调用，实际使用时替换为真实的Neo4j API
    const response = await fetch('/api/neo4j/multi-hop', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query, parameters })
    });
    
    return await response.json();
}

// 2. 转换多跳数据为Sigma格式
function convertMultiHopToSigma(multiHopData) {
    const sigmaData = {
        nodes: [],
        edges: []
    };
    
    // 处理返回的数据
    multiHopData.forEach(record => {
        let networkData;
        
        // 根据查询类型获取数据
        if (record.ego_multi_hop_data) {
            networkData = record.ego_multi_hop_data;
        } else if (record.friend_multi_hop_data) {
            networkData = record.friend_multi_hop_data;
        } else if (record.circle_multi_hop_data) {
            networkData = record.circle_multi_hop_data;
        }
        
        if (!networkData) return;
        
        // 转换节点
        networkData.nodes.forEach(node => {
            if (!sigmaData.nodes.find(n => n.key === String(node.id))) {
                // 根据节点类型设置样式
                let color, size;
                switch(node.type) {
                    case 'ego':
                        color = '#e74c3c'; // 红色
                        size = 20;
                        break;
                    case 'friend':
                        color = '#3498db'; // 蓝色
                        size = 12;
                        break;
                    case 'circle':
                        color = '#9b59b6'; // 紫色
                        size = 15;
                        break;
                    default:
                        color = '#95a5a6'; // 灰色
                        size = 8;
                }
                
                sigmaData.nodes.push({
                    key: String(node.id),
                    label: node.label || String(node.id),
                    tag: node.type,
                    dataset: node.dataset,
                    features: node.features || [],
                    circle_info: node.circle_info,
                    x: Math.random() * 1000,
                    y: Math.random() * 1000,
                    size: size,
                    color: color
                });
            }
        });
        
        // 转换边
        networkData.edges.forEach((edge, edgeIndex) => {
            if (edge.source && edge.target) {
                sigmaData.edges.push({
                    key: `edge_${edgeIndex}_${edge.source}_${edge.target}`,
                    source: String(edge.source),
                    target: String(edge.target),
                    label: edge.type,
                    type: edge.type,
                    dataset: edge.dataset,
                    color: getMultiHopEdgeColor(edge.type),
                    size: getMultiHopEdgeSize(edge.type)
                });
            }
        });
    });
    
    return sigmaData;
}

// 3. 获取多跳边的颜色
function getMultiHopEdgeColor(edgeType) {
    switch(edgeType) {
        case 'HAS_FRIEND': return '#2980b9';
        case 'FRIEND_OF': return '#27ae60';
        case 'BELONGS_TO': return '#8e44ad';
        default: return '#95a5a6';
    }
}

// 4. 获取多跳边的大小
function getMultiHopEdgeSize(edgeType) {
    switch(edgeType) {
        case 'HAS_FRIEND': return 3;
        case 'FRIEND_OF': return 2;
        case 'BELONGS_TO': return 2;
        default: return 1;
    }
}

// 5. 渲染多跳网络
function renderMultiHopNetwork(container, data, startNodeInfo) {
    // 清除现有图形
    if (window.multiHopSigma) {
        window.multiHopSigma.kill();
    }
    
    // 创建Sigma实例
    window.multiHopSigma = new Sigma({
        graph: data,
        container: container,
        settings: {
            defaultNodeColor: '#95a5a6',
            defaultEdgeColor: '#bdc3c7',
            nodesPowRatio: 0.8,
            edgesPowRatio: 0.8,
            drawLabels: true,
            labelThreshold: 6,
            labelSize: 'proportional',
            labelSizeRatio: 1.5,
            mouseWheelEnabled: true,
            doubleClickEnabled: true,
            minNodeSize: 8,
            maxNodeSize: 25,
            minEdgeSize: 1,
            maxEdgeSize: 5,
            animationsTime: 1500
        }
    });
    
    // 添加事件监听
    window.multiHopSigma.bind('clickNode', function(e) {
        const node = e.data.node;
        showMultiHopNodeDetails(node, startNodeInfo);
        highlightMultiHopNeighbors(node);
    });
    
    // 启动布局
    startMultiHopLayout();
    
    return window.multiHopSigma;
}

// 6. 显示多跳节点详情
function showMultiHopNodeDetails(node, startNodeInfo) {
    const detailsHtml = `
        <div class="multi-hop-details">
            <h3>多跳节点详情</h3>
            <p><strong>ID:</strong> ${node.label}</p>
            <p><strong>类型:</strong> ${node.tag}</p>
            <p><strong>数据集:</strong> ${node.dataset}</p>
            ${node.features && node.features.length > 0 ? 
                `<p><strong>特征:</strong> ${node.features.slice(0, 3).join(', ')}</p>` : ''}
            ${node.circle_info ? 
                `<p><strong>圈子ID:</strong> ${node.circle_info.circle_id}</p>
                 <p><strong>圈子大小:</strong> ${node.circle_info.size}</p>` : ''}
            <hr>
            <p><strong>起始节点:</strong> ${startNodeInfo.type} - ${startNodeInfo.id}</p>
        </div>
    `;
    
    document.getElementById('multi-hop-details').innerHTML = detailsHtml;
}

// 7. 高亮多跳邻居节点
function highlightMultiHopNeighbors(centerNode) {
    if (!window.multiHopSigma) return;
    
    const nodeId = centerNode.key;
    const neighbors = [];
    
    // 找到所有邻居
    window.multiHopSigma.graph.edges().forEach(edge => {
        if (edge.source === nodeId) {
            neighbors.push(edge.target);
        } else if (edge.target === nodeId) {
            neighbors.push(edge.source);
        }
    });
    
    // 重置所有节点颜色
    window.multiHopSigma.graph.nodes().forEach(node => {
        if (node.key === nodeId) {
            node.color = '#e74c3c'; // 中心节点红色
        } else if (neighbors.includes(node.key)) {
            node.color = '#f39c12'; // 邻居节点橙色
        } else {
            // 恢复原始颜色
            switch(node.tag) {
                case 'ego': node.color = '#e74c3c'; break;
                case 'friend': node.color = '#3498db'; break;
                case 'circle': node.color = '#9b59b6'; break;
                default: node.color = '#95a5a6';
            }
        }
    });
    
    window.multiHopSigma.refresh();
}

// 8. 启动多跳布局
function startMultiHopLayout() {
    if (window.multiHopSigma) {
        window.multiHopSigma.startForceAtlas2({
            worker: true,
            barnesHutOptimize: true,
            barnesHutTheta: 0.5,
            iterationsPerRender: 1,
            linLogMode: false,
            outboundAttractionDistribution: false,
            adjustSizes: false,
            edgeWeightInfluence: 1,
            scalingRatio: 1,
            strongGravityMode: false,
            gravity: 1
        });
        
        // 15秒后停止布局
        setTimeout(() => {
            if (window.multiHopSigma) {
                window.multiHopSigma.stopForceAtlas2();
            }
        }, 15000);
    }
}

// 9. 主初始化函数
async function initMultiHopVisualization(nodeType, nodeId, dataset, maxHops = 3) {
    try {
        console.log(`正在获取${nodeType}节点${nodeId}的${maxHops}跳网络数据...`);
        showLoading(true);
        
        const multiHopData = await fetchMultiHopNetwork(nodeType, nodeId, dataset, maxHops);
        const sigmaData = convertMultiHopToSigma(multiHopData);
        
        console.log('多跳数据:', sigmaData);
        console.log(`节点数: ${sigmaData.nodes.length}, 边数: ${sigmaData.edges.length}`);
        
        const container = document.getElementById('multi-hop-container');
        const startNodeInfo = { type: nodeType, id: nodeId, dataset: dataset };
        renderMultiHopNetwork(container, sigmaData, startNodeInfo);
        
        // 更新统计信息
        updateMultiHopStats(sigmaData, startNodeInfo, maxHops);
        
        console.log('多跳网络可视化完成！');
        
    } catch (error) {
        console.error('多跳网络初始化失败:', error);
    } finally {
        showLoading(false);
    }
}

// 10. 更新多跳统计信息
function updateMultiHopStats(data, startNodeInfo, maxHops) {
    const nodeTypeCounts = {};
    data.nodes.forEach(node => {
        const type = node.tag || 'unknown';
        nodeTypeCounts[type] = (nodeTypeCounts[type] || 0) + 1;
    });
    
    let statsHtml = `
        <h3>多跳网络统计</h3>
        <p><strong>起始节点:</strong> ${startNodeInfo.type} - ${startNodeInfo.id}</p>
        <p><strong>最大跳数:</strong> ${maxHops}</p>
        <p><strong>总节点数:</strong> ${data.nodes.length}</p>
        <p><strong>总边数:</strong> ${data.edges.length}</p>
        <hr>
    `;
    
    Object.keys(nodeTypeCounts).forEach(type => {
        statsHtml += `<p>${type}节点: ${nodeTypeCounts[type]} 个</p>`;
    });
    
    document.getElementById('multi-hop-stats').innerHTML = statsHtml;
}

// 导出函数供HTML使用
window.initMultiHopVisualization = initMultiHopVisualization;
window.fetchMultiHopNetwork = fetchMultiHopNetwork;
