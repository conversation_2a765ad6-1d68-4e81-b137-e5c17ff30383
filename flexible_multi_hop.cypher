// 灵活的多跳查询 - 支持任意节点作为起点

// ========== 1. 通用多跳查询（支持任意节点类型作为起点） ==========
// 参数：node_id（节点ID），node_type（节点类型：ego/friend/circle），max_hops（最大跳数）

// 示例1：从EGO节点开始的多跳查询
MATCH (start)
WHERE (start:EgoNode AND start.id = 12831)
   OR (start:Node AND start.id = 27985216)
   OR (start:Circle AND start.circle_id = 1 AND start.dataset = '12831')

// 先收集起始节点信息
WITH start,
     CASE
       WHEN 'EgoNode' IN labels(start) THEN start.id
       WHEN 'Node' IN labels(start) THEN start.id
       WHEN 'Circle' IN labels(start) THEN 'circle_' + start.dataset + '_' + start.circle_id
     END as start_id,
     CASE
       WHEN 'EgoNode' IN labels(start) THEN 'ego'
       WHEN 'Node' IN labels(start) THEN 'friend'
       WHEN 'Circle' IN labels(start) THEN 'circle'
     END as start_type,
     start.dataset as start_dataset

// 执行多跳查询
MATCH path = (start)-[*1..3]-(end)
WHERE length(path) <= 3

WITH start_id, start_type, start_dataset,
     collect(DISTINCT nodes(path)) as all_path_nodes,
     collect(DISTINCT relationships(path)) as all_path_rels

UNWIND all_path_nodes as node_list
UNWIND node_list as node
WITH start_id, start_type, start_dataset,
     collect(DISTINCT node) as unique_nodes, all_path_rels

UNWIND all_path_rels as rel_list
UNWIND rel_list as rel

RETURN {
  start_node: {
    id: start_id,
    type: start_type,
    dataset: start_dataset
  },
  nodes: [n IN unique_nodes | {
    id: CASE
      WHEN 'EgoNode' IN labels(n) THEN n.id
      WHEN 'Node' IN labels(n) THEN n.id
      WHEN 'Circle' IN labels(n) THEN 'circle_' + n.dataset + '_' + n.circle_id
    END,
    label: CASE
      WHEN 'EgoNode' IN labels(n) THEN 'EGO_' + n.dataset
      WHEN 'Node' IN labels(n) THEN 'Friend_' + n.id
      WHEN 'Circle' IN labels(n) THEN 'Circle_' + n.circle_id
    END,
    type: CASE
      WHEN 'EgoNode' IN labels(n) THEN 'ego'
      WHEN 'Node' IN labels(n) THEN 'friend'
      WHEN 'Circle' IN labels(n) THEN 'circle'
    END,
    dataset: n.dataset,
    features: CASE
      WHEN 'EgoNode' IN labels(n) OR 'Node' IN labels(n)
      THEN n.active_features[0..3]
      ELSE []
    END,
    circle_info: CASE
      WHEN 'Circle' IN labels(n)
      THEN {circle_id: n.circle_id, size: n.size}
      ELSE null
    END
  }],
  edges: collect(DISTINCT {
    source: CASE
      WHEN 'EgoNode' IN labels(startNode(rel)) THEN startNode(rel).id
      WHEN 'Node' IN labels(startNode(rel)) THEN startNode(rel).id
      WHEN 'Circle' IN labels(startNode(rel)) THEN 'circle_' + startNode(rel).dataset + '_' + startNode(rel).circle_id
    END,
    target: CASE
      WHEN 'EgoNode' IN labels(endNode(rel)) THEN endNode(rel).id
      WHEN 'Node' IN labels(endNode(rel)) THEN endNode(rel).id
      WHEN 'Circle' IN labels(endNode(rel)) THEN 'circle_' + endNode(rel).dataset + '_' + endNode(rel).circle_id
    END,
    type: type(rel),
    dataset: startNode(rel).dataset
  })
} as multi_hop_result;

// ========== 2. 从EGO节点开始的多跳查询 ==========
// 输入：ego_id（如：12831）
WITH 12831 as ego_id

MATCH (start:EgoNode {id: ego_id})
MATCH path = (start)-[*1..3]-(end)
WHERE length(path) <= 3

WITH ego_id, collect(DISTINCT nodes(path)) as all_path_nodes,
     collect(DISTINCT relationships(path)) as all_path_rels

UNWIND all_path_nodes as node_list
UNWIND node_list as node
WITH ego_id, collect(DISTINCT node) as unique_nodes, all_path_rels

UNWIND all_path_rels as rel_list
UNWIND rel_list as rel

RETURN {
  query_type: 'ego_multi_hop',
  start_ego: ego_id,
  nodes: [n IN unique_nodes | {
    id: CASE
      WHEN 'EgoNode' IN labels(n) THEN n.id
      WHEN 'Node' IN labels(n) THEN n.id
      WHEN 'Circle' IN labels(n) THEN 'circle_' + n.dataset + '_' + n.circle_id
    END,
    label: CASE
      WHEN 'EgoNode' IN labels(n) THEN 'EGO_' + n.dataset
      WHEN 'Node' IN labels(n) THEN 'Friend_' + n.id
      WHEN 'Circle' IN labels(n) THEN 'Circle_' + n.circle_id
    END,
    type: CASE
      WHEN 'EgoNode' IN labels(n) THEN 'ego'
      WHEN 'Node' IN labels(n) THEN 'friend'
      WHEN 'Circle' IN labels(n) THEN 'circle'
    END,
    dataset: n.dataset,
    features: CASE
      WHEN 'EgoNode' IN labels(n) OR 'Node' IN labels(n)
      THEN n.active_features[0..3]
      ELSE []
    END
  }],
  edges: collect(DISTINCT {
    source: CASE
      WHEN 'Circle' IN labels(startNode(rel))
      THEN 'circle_' + startNode(rel).dataset + '_' + startNode(rel).circle_id
      ELSE startNode(rel).id
    END,
    target: CASE
      WHEN 'Circle' IN labels(endNode(rel))
      THEN 'circle_' + endNode(rel).dataset + '_' + endNode(rel).circle_id
      ELSE endNode(rel).id
    END,
    type: type(rel),
    dataset: startNode(rel).dataset
  })
} as ego_multi_hop_data;

// ========== 3. 从朋友节点开始的多跳查询 ==========
// 输入：friend_id（如：27985216），dataset（如：'12831'）
WITH 27985216 as friend_id, '12831' as dataset

MATCH (start:Node {id: friend_id, dataset: dataset})
MATCH path = (start)-[*1..3]-(end)
WHERE length(path) <= 3

WITH friend_id, dataset, collect(DISTINCT nodes(path)) as all_path_nodes,
     collect(DISTINCT relationships(path)) as all_path_rels

UNWIND all_path_nodes as node_list
UNWIND node_list as node
WITH friend_id, dataset, collect(DISTINCT node) as unique_nodes, all_path_rels

UNWIND all_path_rels as rel_list
UNWIND rel_list as rel

RETURN {
  query_type: 'friend_multi_hop',
  start_friend: friend_id,
  start_dataset: dataset,
  nodes: [n IN unique_nodes | {
    id: CASE
      WHEN 'EgoNode' IN labels(n) THEN n.id
      WHEN 'Node' IN labels(n) THEN n.id
      WHEN 'Circle' IN labels(n) THEN 'circle_' + n.dataset + '_' + n.circle_id
    END,
    label: CASE
      WHEN 'EgoNode' IN labels(n) THEN 'EGO_' + n.dataset
      WHEN 'Node' IN labels(n) THEN 'Friend_' + n.id
      WHEN 'Circle' IN labels(n) THEN 'Circle_' + n.circle_id
    END,
    type: CASE
      WHEN 'EgoNode' IN labels(n) THEN 'ego'
      WHEN 'Node' IN labels(n) THEN 'friend'
      WHEN 'Circle' IN labels(n) THEN 'circle'
    END,
    dataset: n.dataset,
    features: CASE
      WHEN 'EgoNode' IN labels(n) OR 'Node' IN labels(n)
      THEN n.active_features[0..3]
      ELSE []
    END
  }],
  edges: collect(DISTINCT {
    source: CASE
      WHEN 'Circle' IN labels(startNode(rel))
      THEN 'circle_' + startNode(rel).dataset + '_' + startNode(rel).circle_id
      ELSE startNode(rel).id
    END,
    target: CASE
      WHEN 'Circle' IN labels(endNode(rel))
      THEN 'circle_' + endNode(rel).dataset + '_' + endNode(rel).circle_id
      ELSE endNode(rel).id
    END,
    type: type(rel),
    dataset: startNode(rel).dataset
  })
} as friend_multi_hop_data;

// ========== 4. 从圈子开始的多跳查询 ==========
// 输入：circle_id（如：1），dataset（如：'12831'）
WITH 1 as circle_id, '12831' as dataset

MATCH (start:Circle {circle_id: circle_id, dataset: dataset})
MATCH path = (start)-[*1..3]-(end)
WHERE length(path) <= 3

WITH circle_id, dataset, collect(DISTINCT nodes(path)) as all_path_nodes,
     collect(DISTINCT relationships(path)) as all_path_rels

UNWIND all_path_nodes as node_list
UNWIND node_list as node
WITH circle_id, dataset, collect(DISTINCT node) as unique_nodes, all_path_rels

UNWIND all_path_rels as rel_list
UNWIND rel_list as rel

RETURN {
  query_type: 'circle_multi_hop',
  start_circle: circle_id,
  start_dataset: dataset,
  nodes: [n IN unique_nodes | {
    id: CASE
      WHEN 'EgoNode' IN labels(n) THEN n.id
      WHEN 'Node' IN labels(n) THEN n.id
      WHEN 'Circle' IN labels(n) THEN 'circle_' + n.dataset + '_' + n.circle_id
    END,
    label: CASE
      WHEN 'EgoNode' IN labels(n) THEN 'EGO_' + n.dataset
      WHEN 'Node' IN labels(n) THEN 'Friend_' + n.id
      WHEN 'Circle' IN labels(n) THEN 'Circle_' + n.circle_id
    END,
    type: CASE
      WHEN 'EgoNode' IN labels(n) THEN 'ego'
      WHEN 'Node' IN labels(n) THEN 'friend'
      WHEN 'Circle' IN labels(n) THEN 'circle'
    END,
    dataset: n.dataset,
    features: CASE
      WHEN 'EgoNode' IN labels(n) OR 'Node' IN labels(n)
      THEN n.active_features[0..3]
      ELSE []
    END,
    circle_info: CASE
      WHEN 'Circle' IN labels(n)
      THEN {circle_id: n.circle_id, size: n.size}
      ELSE null
    END
  }],
  edges: collect(DISTINCT {
    source: CASE
      WHEN 'Circle' IN labels(startNode(rel))
      THEN 'circle_' + startNode(rel).dataset + '_' + startNode(rel).circle_id
      ELSE startNode(rel).id
    END,
    target: CASE
      WHEN 'Circle' IN labels(endNode(rel))
      THEN 'circle_' + endNode(rel).dataset + '_' + endNode(rel).circle_id
      ELSE endNode(rel).id
    END,
    type: type(rel),
    dataset: startNode(rel).dataset
  })
} as circle_multi_hop_data;

// ========== 5. 可配置的多跳查询模板 ==========
// 这个查询可以通过修改参数来适应不同的起始节点
// 使用方法：修改下面的参数值，然后运行查询

// 配置参数
WITH {
  node_type: 'ego',        // 'ego', 'friend', 'circle'
  node_id: 12831,          // 节点ID
  dataset: '12831',        // 数据集（对于friend和circle节点需要）
  circle_id: null,         // 圈子ID（仅对circle节点需要）
  max_hops: 3              // 最大跳数
} as config

// 根据配置查找起始节点
MATCH (start)
WHERE (config.node_type = 'ego' AND start:EgoNode AND start.id = config.node_id)
   OR (config.node_type = 'friend' AND start:Node AND start.id = config.node_id AND start.dataset = config.dataset)
   OR (config.node_type = 'circle' AND start:Circle AND start.circle_id = config.circle_id AND start.dataset = config.dataset)

// 先收集起始节点信息和配置
WITH config,
     CASE
       WHEN 'EgoNode' IN labels(start) THEN start.id
       WHEN 'Node' IN labels(start) THEN start.id
       WHEN 'Circle' IN labels(start) THEN 'circle_' + start.dataset + '_' + start.circle_id
     END as start_node_id,
     CASE
       WHEN 'EgoNode' IN labels(start) THEN 'ego'
       WHEN 'Node' IN labels(start) THEN 'friend'
       WHEN 'Circle' IN labels(start) THEN 'circle'
     END as start_node_type,
     start.dataset as start_node_dataset,
     start

// 执行多跳查询
MATCH path = (start)-[*1..config.max_hops]-(end)
WHERE length(path) <= config.max_hops

WITH config, start_node_id, start_node_type, start_node_dataset,
     collect(DISTINCT nodes(path)) as all_path_nodes,
     collect(DISTINCT relationships(path)) as all_path_rels

UNWIND all_path_nodes as node_list
UNWIND node_list as node
WITH config, start_node_id, start_node_type, start_node_dataset,
     collect(DISTINCT node) as unique_nodes, all_path_rels

UNWIND all_path_rels as rel_list
UNWIND rel_list as rel

RETURN {
  config: config,
  start_node_info: {
    id: start_node_id,
    type: start_node_type,
    dataset: start_node_dataset
  },
  total_nodes: size(unique_nodes),
  total_edges: size(collect(DISTINCT rel)),
  nodes: [n IN unique_nodes | {
    id: CASE
      WHEN 'EgoNode' IN labels(n) THEN n.id
      WHEN 'Node' IN labels(n) THEN n.id
      WHEN 'Circle' IN labels(n) THEN 'circle_' + n.dataset + '_' + n.circle_id
    END,
    label: CASE
      WHEN 'EgoNode' IN labels(n) THEN 'EGO_' + n.dataset
      WHEN 'Node' IN labels(n) THEN 'Friend_' + n.id
      WHEN 'Circle' IN labels(n) THEN 'Circle_' + n.circle_id
    END,
    type: CASE
      WHEN 'EgoNode' IN labels(n) THEN 'ego'
      WHEN 'Node' IN labels(n) THEN 'friend'
      WHEN 'Circle' IN labels(n) THEN 'circle'
    END,
    dataset: n.dataset
  }],
  edges: collect(DISTINCT {
    source: CASE
      WHEN 'Circle' IN labels(startNode(rel))
      THEN 'circle_' + startNode(rel).dataset + '_' + startNode(rel).circle_id
      ELSE startNode(rel).id
    END,
    target: CASE
      WHEN 'Circle' IN labels(endNode(rel))
      THEN 'circle_' + endNode(rel).dataset + '_' + endNode(rel).circle_id
      ELSE endNode(rel).id
    END,
    type: type(rel)
  })
} as configurable_multi_hop;
