const neo4j = require('neo4j-driver');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 配置信息 - 请修改这些参数
const NEO4J_URI = 'bolt://localhost:7687';
const NEO4J_USERNAME = 'neo4j';
const NEO4J_PASSWORD = '12345678'; // 修改为你的密码
const DATA_FOLDER = './twitter-min'; // 修改为你的数据文件夹路径

class Neo4jImporter {
    constructor(uri, username, password) {
        this.driver = neo4j.driver(uri, neo4j.auth.basic(username, password));
    }

    async close() {
        await this.driver.close();
    }

    async createConstraintsAndIndexes() {
        const session = this.driver.session();
        try {
            // 创建约束和索引
            await session.run('CREATE CONSTRAINT node_id IF NOT EXISTS FOR (n:Node) REQUIRE (n.id, n.dataset) IS UNIQUE');
            await session.run('CREATE CONSTRAINT ego_id IF NOT EXISTS FOR (n:EgoNode) REQUIRE n.id IS UNIQUE');
            await session.run('CREATE INDEX node_dataset IF NOT EXISTS FOR (n:Node) ON (n.dataset)');
            await session.run('CREATE INDEX ego_dataset IF NOT EXISTS FOR (n:EgoNode) ON (n.dataset)');
            console.log('约束和索引创建完成');
        } finally {
            await session.close();
        }
    }

    async clearDatabase() {
        const session = this.driver.session();
        try {
            await session.run('MATCH (n) DETACH DELETE n');
            console.log('数据库已清空');
        } finally {
            await session.close();
        }
    }

    async importSingleDataset(datasetId, folderPath) {
        const files = {
            circles: `${datasetId}.circles`,
            edges: `${datasetId}.edges`,
            egofeat: `${datasetId}.egofeat`,
            feat: `${datasetId}.feat`,
            featnames: `${datasetId}.featnames`
        };

        // 检查所有文件是否存在
        for (const [fileType, filename] of Object.entries(files)) {
            const filepath = path.join(folderPath, filename);
            if (!fs.existsSync(filepath)) {
                console.warn(`文件不存在: ${filepath}`);
                return false;
            }
        }

        const session = this.driver.session();
        
        try {
            console.log(`开始导入数据集: ${datasetId}`);
            
            // 1. 读取并存储特征名称映射（临时存储）
            const featureMapping = await this.loadFeatureMapping(folderPath, files.featnames);
            console.log(`  加载了 ${Object.keys(featureMapping).length} 个特征名称`);
            
            // 2. 导入特征名称到数据库
            await this.importFeatnames(session, datasetId, featureMapping);
            
            // 3. 导入ego节点特征
            await this.importEgoFeatures(session, datasetId, folderPath, files.egofeat, featureMapping);
            
            // 4. 导入节点特征（朋友节点）
            await this.importNodeFeatures(session, datasetId, folderPath, files.feat, featureMapping);
            
            // 5. 建立ego节点与朋友节点的连接
            await this.createEgoToFriendsConnections(session, datasetId);
            
            // 6. 导入朋友之间的边关系
            await this.importEdges(session, datasetId, folderPath, files.edges);
            
            // 7. 导入圈子信息
            await this.importCircles(session, datasetId, folderPath, files.circles);
            
            console.log(`成功导入数据集: ${datasetId}`);
            return true;
            
        } catch (error) {
            console.error(`导入数据集 ${datasetId} 时出错:`, error.message);
            return false;
        } finally {
            await session.close();
        }
    }

    async loadFeatureMapping(folderPath, filename) {
        const filepath = path.join(folderPath, filename);
        const featureMapping = {};
        
        const fileStream = fs.createReadStream(filepath);
        const rl = readline.createInterface({
            input: fileStream,
            crlfDelay: Infinity
        });

        for await (const line of rl) {
            const parts = line.trim().split(' ', 2);
            if (parts.length >= 2) {
                const index = parseInt(parts[0]);
                const name = parts[1];
                featureMapping[index] = name;
            }
        }
        
        return featureMapping;
    }

    async importFeatnames(session, datasetId, featureMapping) {
        const featnames = Object.entries(featureMapping).map(([index, name]) => ({
            index: neo4j.int(parseInt(index)),
            name: name,
            dataset: datasetId
        }));

        if (featnames.length > 0) {
            await session.run(`
                UNWIND $featnames AS feat
                CREATE (f:FeatureName {
                    dataset: feat.dataset,
                    index: feat.index,
                    name: feat.name
                })
            `, { featnames });
        }
    }

    async importEgoFeatures(session, datasetId, folderPath, filename, featureMapping) {
        const filepath = path.join(folderPath, filename);
        const content = fs.readFileSync(filepath, 'utf8').trim();
        
        if (content) {
            // 解析特征向量（0和1的序列）
            const features = content.split(/\s+/).map(x => parseInt(x));
            
            // 提取激活的特征
            const activeFeatures = [];
            const namedProperties = {};
            
            features.forEach((value, index) => {
                if (value === 1 && featureMapping[index]) {
                    const featureName = featureMapping[index];
                    activeFeatures.push(featureName);
                    // 创建清理后的属性名
                    const cleanName = featureName.replace(/[^a-zA-Z0-9_]/g, '_');
                    namedProperties[`feat_${cleanName}`] = true;
                }
            });
            
            // 构建完整的ego节点数据
            const egoNodeData = {
                id: neo4j.int(parseInt(datasetId)),
                dataset: datasetId,
                features: features,
                feature_count: neo4j.int(features.length),
                active_features: activeFeatures,
                active_feature_count: neo4j.int(activeFeatures.length),
                ...namedProperties
            };
            
            // 创建ego节点
            await session.run(`
                CREATE (ego:EgoNode)
                SET ego = $egoNodeData
            `, { egoNodeData });
            
            console.log(`  创建了ego节点，包含 ${features.length} 个特征，激活 ${activeFeatures.length} 个`);
        }
    }

    async importNodeFeatures(session, datasetId, folderPath, filename, featureMapping) {
        const filepath = path.join(folderPath, filename);
        const allNodes = [];
        
        const fileStream = fs.createReadStream(filepath);
        const rl = readline.createInterface({
            input: fileStream,
            crlfDelay: Infinity
        });

        for await (const line of rl) {
            const parts = line.trim().split(/\s+/);
            if (parts.length > 0) {
                const nodeId = parseInt(parts[0]);
                const features = parts.slice(1).map(x => parseInt(x));
                
                // 提取激活的特征和创建命名属性
                const activeFeatures = [];
                const namedProperties = {};
                
                features.forEach((value, index) => {
                    if (value === 1 && featureMapping[index]) {
                        const featureName = featureMapping[index];
                        activeFeatures.push(featureName);
                        const cleanName = featureName.replace(/[^a-zA-Z0-9_]/g, '_');
                        namedProperties[`feat_${cleanName}`] = true;
                    }
                });
                
                // 构建完整的节点数据
                const nodeData = {
                    id: neo4j.int(nodeId),
                    dataset: datasetId,
                    features: features,
                    feature_count: neo4j.int(features.length),
                    active_features: activeFeatures,
                    active_feature_count: neo4j.int(activeFeatures.length),
                    ...namedProperties
                };
                
                allNodes.push(nodeData);
            }
        }

        // 批量创建节点
        const batchSize = 300; // 保守的批次大小
        for (let i = 0; i < allNodes.length; i += batchSize) {
            const batch = allNodes.slice(i, i + batchSize);
            
            await session.run(`
                UNWIND $batch AS nodeData
                CREATE (n:Node)
                SET n = nodeData
            `, { batch });
        }
        
        console.log(`  导入了 ${allNodes.length} 个朋友节点`);
    }

    async createEgoToFriendsConnections(session, datasetId) {
        await session.run(`
            MATCH (ego:EgoNode {id: $egoId, dataset: $dataset})
            MATCH (friend:Node {dataset: $dataset})
            CREATE (ego)-[:HAS_FRIEND {dataset: $dataset}]->(friend)
        `, {
            egoId: neo4j.int(parseInt(datasetId)),
            dataset: datasetId
        });
        
        console.log(`  建立了ego节点与朋友节点的连接`);
    }

    async importEdges(session, datasetId, folderPath, filename) {
        const filepath = path.join(folderPath, filename);
        const edges = [];
        
        const fileStream = fs.createReadStream(filepath);
        const rl = readline.createInterface({
            input: fileStream,
            crlfDelay: Infinity
        });

        for await (const line of rl) {
            const parts = line.trim().split(/\s+/);
            if (parts.length >= 2) {
                const source = parseInt(parts[0]);
                const target = parseInt(parts[1]);
                edges.push({
                    source: neo4j.int(source),
                    target: neo4j.int(target),
                    dataset: datasetId
                });
            }
        }

        // 批量创建朋友之间的边关系
        const batchSize = 1000;
        for (let i = 0; i < edges.length; i += batchSize) {
            const batch = edges.slice(i, i + batchSize);
            await session.run(`
                UNWIND $edges AS edge
                MATCH (a:Node {id: edge.source, dataset: edge.dataset})
                MATCH (b:Node {id: edge.target, dataset: edge.dataset})
                CREATE (a)-[:FRIEND_OF {dataset: edge.dataset}]->(b)
            `, { edges: batch });
        }
        
        console.log(`  导入了 ${edges.length} 条朋友关系`);
    }

    async importCircles(session, datasetId, folderPath, filename) {
        const filepath = path.join(folderPath, filename);
        
        try {
            const fileStream = fs.createReadStream(filepath);
            const rl = readline.createInterface({
                input: fileStream,
                crlfDelay: Infinity
            });

            let circleId = 0;
            for await (const line of rl) {
                const parts = line.trim().split(/\s+/);
                if (parts.length > 0) {
                    const members = parts.map(x => neo4j.int(parseInt(x)));
                    
                    await session.run(`
                        CREATE (c:Circle {
                            dataset: $dataset,
                            circle_id: $circleId,
                            size: $size
                        })
                        WITH c
                        UNWIND $members AS memberId
                        MATCH (n:Node {id: memberId, dataset: $dataset})
                        CREATE (n)-[:BELONGS_TO]->(c)
                    `, {
                        dataset: datasetId,
                        circleId: neo4j.int(circleId),
                        size: neo4j.int(members.length),
                        members: members
                    });
                    
                    circleId++;
                }
            }
        } catch (error) {
            console.warn(`导入圈子信息时出错 ${datasetId}:`, error.message);
        }
    }
}

function getDatasetIds(folderPath) {
    const files = fs.readdirSync(folderPath);
    const datasetIds = new Set();
    
    files.forEach(filename => {
        if (filename.includes('.')) {
            const datasetId = filename.split('.')[0];
            datasetIds.add(datasetId);
        }
    });
    
    return Array.from(datasetIds).sort();
}

async function main() {
    const importer = new Neo4jImporter(NEO4J_URI, NEO4J_USERNAME, NEO4J_PASSWORD);
    
    try {
        // 创建约束和索引
        console.log('创建数据库约束和索引...');
        await importer.createConstraintsAndIndexes();
        
        // 获取所有数据集ID
        const datasetIds = getDatasetIds(DATA_FOLDER);
        console.log(`找到 ${datasetIds.length} 个数据集`);
        
        // 可选：清空数据库（谨慎使用）
        // await importer.clearDatabase();
        
        // 批量导入
        let successCount = 0;
        const failedDatasets = [];
        
        for (let i = 0; i < datasetIds.length; i++) {
            const datasetId = datasetIds[i];
            console.log(`进度: ${i + 1}/${datasetIds.length} - 处理数据集: ${datasetId}`);
            
            if (await importer.importSingleDataset(datasetId, DATA_FOLDER)) {
                successCount++;
            } else {
                failedDatasets.push(datasetId);
            }
        }
        
        console.log(`导入完成! 成功: ${successCount}, 失败: ${failedDatasets.length}`);
        
        if (failedDatasets.length > 0) {
            console.log('失败的数据集:', failedDatasets.slice(0, 10));
        }
        
    } catch (error) {
        console.error('导入过程中出现错误:', error);
    } finally {
        await importer.close();
    }
}

// 运行程序
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { Neo4jImporter, getDatasetIds };