// 多跳查询的Sigma.js集成

// 1. 多跳查询API函数
async function fetchMultiHopData(startNodeId, startDataset, maxHops = 3) {
    const query = `
        MATCH (start:EgoNode {dataset: $startDataset})
        
        // 第1跳：获取直接朋友
        MATCH (start)-[:HAS_FRIEND]->(friend1:Node)
        WITH start, collect(friend1)[0..10] as first_hop_friends
        
        // 第2跳：获取朋友的朋友
        UNWIND first_hop_friends as friend1
        MATCH (friend1)-[:FRIEND_OF]-(friend2:Node)
        WHERE friend2.dataset = start.dataset
        WITH start, first_hop_friends, collect(DISTINCT friend2)[0..15] as second_hop_friends
        
        // 第3跳：获取圈子信息
        UNWIND (first_hop_friends + second_hop_friends) as friend
        MATCH (friend)-[:BELONGS_TO]->(circle:Circle)
        WITH start, first_hop_friends, second_hop_friends, collect(DISTINCT circle) as circles
        
        RETURN {
          nodes: [{
            id: start.id,
            label: 'EGO_' + start.dataset,
            type: 'ego',
            dataset: start.dataset,
            features: start.active_features[0..3],
            hop: 0
          }] + 
          [f IN first_hop_friends | {
            id: f.id,
            label: 'Friend_' + f.id,
            type: 'friend',
            dataset: f.dataset,
            features: f.active_features[0..2],
            hop: 1
          }] +
          [f IN second_hop_friends | {
            id: f.id,
            label: 'Friend_' + f.id,
            type: 'friend',
            dataset: f.dataset,
            features: f.active_features[0..2],
            hop: 2
          }] +
          [c IN circles | {
            id: 'circle_' + c.dataset + '_' + c.circle_id,
            label: 'Circle_' + c.circle_id,
            type: 'circle',
            dataset: c.dataset,
            circle_id: c.circle_id,
            size: c.size,
            hop: 3
          }],
          
          edges: [f IN first_hop_friends | {
            source: start.id,
            target: f.id,
            type: 'HAS_FRIEND',
            dataset: start.dataset
          }] +
          [f1 IN first_hop_friends | 
            [f2 IN second_hop_friends WHERE EXISTS((f1)-[:FRIEND_OF]-(f2)) | {
              source: f1.id,
              target: f2.id,
              type: 'FRIEND_OF',
              dataset: f1.dataset
            }]
          ][0] +
          [c IN circles |
            [f IN (first_hop_friends + second_hop_friends) WHERE EXISTS((f)-[:BELONGS_TO]->(c)) | {
              source: f.id,
              target: 'circle_' + c.dataset + '_' + c.circle_id,
              type: 'BELONGS_TO',
              dataset: c.dataset
            }]
          ][0]
        } as three_hop_network;
    `;
    
    const response = await fetch('/api/neo4j/multi-hop', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
            query, 
            parameters: { 
                startDataset: startDataset,
                maxHops: maxHops 
            }
        })
    });
    
    return await response.json();
}

// 2. 转换多跳数据为Sigma格式
function convertMultiHopToSigma(multiHopData) {
    const sigmaData = {
        nodes: [],
        edges: []
    };
    
    // 处理节点，根据跳数设置不同的样式
    multiHopData.forEach(record => {
        const networkData = record.three_hop_network;
        
        networkData.nodes.forEach(node => {
            if (!sigmaData.nodes.find(n => n.key === String(node.id))) {
                // 根据跳数设置颜色和大小
                let color, size;
                switch(node.hop) {
                    case 0: // 起始节点
                        color = '#e74c3c'; // 红色
                        size = 20;
                        break;
                    case 1: // 第1跳
                        color = '#3498db'; // 蓝色
                        size = 15;
                        break;
                    case 2: // 第2跳
                        color = '#2ecc71'; // 绿色
                        size = 12;
                        break;
                    case 3: // 第3跳
                        color = '#9b59b6'; // 紫色
                        size = 10;
                        break;
                    default:
                        color = '#95a5a6'; // 灰色
                        size = 8;
                }
                
                sigmaData.nodes.push({
                    key: String(node.id),
                    label: node.label || String(node.id),
                    tag: node.type,
                    dataset: node.dataset,
                    features: node.features || [],
                    hop: node.hop,
                    x: Math.random() * 1000,
                    y: Math.random() * 1000,
                    size: size,
                    color: color
                });
            }
        });
        
        // 处理边
        networkData.edges.forEach((edge, edgeIndex) => {
            if (edge.source && edge.target) {
                sigmaData.edges.push({
                    key: `edge_${edgeIndex}_${edge.source}_${edge.target}`,
                    source: String(edge.source),
                    target: String(edge.target),
                    label: edge.type,
                    type: edge.type,
                    dataset: edge.dataset,
                    color: getEdgeColor(edge.type),
                    size: getEdgeSize(edge.type)
                });
            }
        });
    });
    
    return sigmaData;
}

// 3. 获取边的颜色
function getEdgeColor(edgeType) {
    switch(edgeType) {
        case 'HAS_FRIEND': return '#2980b9';
        case 'FRIEND_OF': return '#27ae60';
        case 'BELONGS_TO': return '#8e44ad';
        default: return '#95a5a6';
    }
}

// 4. 获取边的大小
function getEdgeSize(edgeType) {
    switch(edgeType) {
        case 'HAS_FRIEND': return 3;
        case 'FRIEND_OF': return 2;
        case 'BELONGS_TO': return 2;
        default: return 1;
    }
}

// 5. 渲染多跳网络
function renderMultiHopNetwork(container, data, options = {}) {
    // 清除现有图形
    if (window.multiHopSigma) {
        window.multiHopSigma.kill();
    }
    
    // 创建Sigma实例
    window.multiHopSigma = new Sigma({
        graph: data,
        container: container,
        settings: {
            defaultNodeColor: '#95a5a6',
            defaultEdgeColor: '#bdc3c7',
            nodesPowRatio: 0.8,
            edgesPowRatio: 0.8,
            drawLabels: true,
            labelThreshold: 6,
            labelSize: 'proportional',
            labelSizeRatio: 1.5,
            mouseWheelEnabled: true,
            doubleClickEnabled: true,
            minNodeSize: 6,
            maxNodeSize: 25,
            minEdgeSize: 1,
            maxEdgeSize: 5,
            animationsTime: 1500,
            ...options
        }
    });
    
    // 添加事件监听
    window.multiHopSigma.bind('clickNode', function(e) {
        const node = e.data.node;
        showMultiHopNodeDetails(node);
        
        // 高亮相关节点
        highlightNodeNeighbors(node);
    });
    
    // 启动布局
    startMultiHopLayout();
    
    return window.multiHopSigma;
}

// 6. 显示多跳节点详情
function showMultiHopNodeDetails(node) {
    const detailsHtml = `
        <div class="multi-hop-details">
            <h3>多跳节点详情</h3>
            <p><strong>ID:</strong> ${node.label}</p>
            <p><strong>类型:</strong> ${node.tag}</p>
            <p><strong>跳数:</strong> ${node.hop}</p>
            <p><strong>数据集:</strong> ${node.dataset}</p>
            ${node.features ? `<p><strong>特征:</strong> ${node.features.slice(0, 3).join(', ')}</p>` : ''}
            ${node.circle_id ? `<p><strong>圈子ID:</strong> ${node.circle_id}</p>` : ''}
        </div>
    `;
    
    document.getElementById('multi-hop-details').innerHTML = detailsHtml;
}

// 7. 高亮邻居节点
function highlightNodeNeighbors(centerNode) {
    if (!window.multiHopSigma) return;
    
    const nodeId = centerNode.key;
    const neighbors = [];
    
    // 找到所有邻居
    window.multiHopSigma.graph.edges().forEach(edge => {
        if (edge.source === nodeId) {
            neighbors.push(edge.target);
        } else if (edge.target === nodeId) {
            neighbors.push(edge.source);
        }
    });
    
    // 重置所有节点颜色
    window.multiHopSigma.graph.nodes().forEach(node => {
        if (node.key === nodeId) {
            node.color = '#e74c3c'; // 中心节点红色
        } else if (neighbors.includes(node.key)) {
            node.color = '#f39c12'; // 邻居节点橙色
        } else {
            node.color = '#bdc3c7'; // 其他节点灰色
        }
    });
    
    window.multiHopSigma.refresh();
}

// 8. 启动多跳布局
function startMultiHopLayout() {
    if (window.multiHopSigma) {
        window.multiHopSigma.startForceAtlas2({
            worker: true,
            barnesHutOptimize: true,
            barnesHutTheta: 0.5,
            iterationsPerRender: 1,
            linLogMode: false,
            outboundAttractionDistribution: false,
            adjustSizes: false,
            edgeWeightInfluence: 1,
            scalingRatio: 1,
            strongGravityMode: false,
            gravity: 1
        });
        
        // 15秒后停止布局
        setTimeout(() => {
            if (window.multiHopSigma) {
                window.multiHopSigma.stopForceAtlas2();
            }
        }, 15000);
    }
}

// 9. 主初始化函数
async function initMultiHopNetwork(startDataset = '12831', maxHops = 3) {
    try {
        console.log(`正在获取${maxHops}跳网络数据...`);
        showLoading(true);
        
        const multiHopData = await fetchMultiHopData(null, startDataset, maxHops);
        const sigmaData = convertMultiHopToSigma(multiHopData);
        
        console.log('多跳数据:', sigmaData);
        console.log(`节点数: ${sigmaData.nodes.length}, 边数: ${sigmaData.edges.length}`);
        
        const container = document.getElementById('multi-hop-container');
        renderMultiHopNetwork(container, sigmaData);
        
        // 更新统计信息
        updateMultiHopStats(sigmaData, maxHops);
        
        console.log('多跳网络渲染完成！');
        
    } catch (error) {
        console.error('多跳网络初始化失败:', error);
    } finally {
        showLoading(false);
    }
}

// 10. 更新多跳统计信息
function updateMultiHopStats(data, maxHops) {
    const hopCounts = {};
    data.nodes.forEach(node => {
        const hop = node.hop || 0;
        hopCounts[hop] = (hopCounts[hop] || 0) + 1;
    });
    
    let statsHtml = `<h3>多跳网络统计</h3>`;
    for (let i = 0; i <= maxHops; i++) {
        statsHtml += `<p>第${i}跳: ${hopCounts[i] || 0} 个节点</p>`;
    }
    statsHtml += `<p>总边数: ${data.edges.length}</p>`;
    
    document.getElementById('multi-hop-stats').innerHTML = statsHtml;
}

// 导出函数
window.initMultiHopNetwork = initMultiHopNetwork;
window.fetchMultiHopData = fetchMultiHopData;
