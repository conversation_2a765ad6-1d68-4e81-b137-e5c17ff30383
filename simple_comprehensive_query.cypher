// 简单综合查询 - 一次性展示所有节点数据

// 选择第一个有朋友的用户展示完整网络
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
WHERE friend_count > 10
ORDER BY friend_count DESC
LIMIT 1

// 获取完整网络数据
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
OPTIONAL MATCH (friend)-[:FRIEND_OF]-(other_friend:Node)
WHERE other_friend.dataset = ego.dataset
OPTIONAL MATCH (friend)-[:BELONGS_TO]->(circle:Circle)

RETURN 
  // EGO用户信息
  ego.dataset as user_dataset,
  ego.id as ego_user_id,
  ego.active_features[0..5] as ego_features,
  
  // 朋友信息
  collect(DISTINCT {
    friend_id: friend.id,
    friend_features: friend.active_features[0..3]
  })[0..15] as friends_list,
  
  // 朋友关系
  collect(DISTINCT {
    friend1: friend.id,
    friend2: other_friend.id
  })[0..10] as friend_relationships,
  
  // 社交圈子
  collect(DISTINCT {
    circle_id: circle.circle_id,
    circle_size: circle.size,
    member: friend.id
  })[0..10] as social_circles;
