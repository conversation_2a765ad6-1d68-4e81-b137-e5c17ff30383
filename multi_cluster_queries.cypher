// 多簇网络查询 - 显示多个EGO用户形成的社交网络簇

// ========== 1. 多个EGO用户的网络簇 ==========
// 显示前5个最活跃用户的网络簇
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
WHERE friend_count > 5 AND friend_count < 30
ORDER BY friend_count DESC
LIMIT 5
MATCH p = (ego)-[:HAS_FRIEND]->(friend:Node)
RETURN p;

// ========== 2. 随机多簇网络 ==========
// 随机选择多个用户显示网络簇
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
WHERE friend_count > 8 AND friend_count < 25
WITH ego, friend_count, rand() as r
ORDER BY r
LIMIT 6
MATCH p = (ego)-[:HAS_FRIEND]->(friend:Node)
RETURN p;

// ========== 3. 完整多簇网络（包含朋友关系） ==========
// 显示多个EGO及其朋友之间的关系
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
WHERE friend_count > 10 AND friend_count < 20
ORDER BY friend_count DESC
LIMIT 4
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
WITH ego, collect(friend)[0..8] as sample_friends
UNWIND sample_friends as friend
MATCH p1 = (ego)-[:HAS_FRIEND]->(friend)
OPTIONAL MATCH p2 = (friend)-[:FRIEND_OF]-(other:Node)
WHERE other IN sample_friends
RETURN p1, p2;

// ========== 4. 多簇社交圈子网络 ==========
// 显示多个用户的社交圈子
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)-[:BELONGS_TO]->(circle:Circle)
WITH ego, count(DISTINCT circle) as circle_count
WHERE circle_count > 2
ORDER BY circle_count DESC
LIMIT 5
MATCH p1 = (ego)-[:HAS_FRIEND]->(friend:Node)
MATCH p2 = (friend)-[:BELONGS_TO]->(circle:Circle)
RETURN p1, p2;

// ========== 5. 不同大小的网络簇对比 ==========
// 显示小、中、大不同规模的网络簇
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
WHERE friend_count > 5
WITH ego, friend_count,
     CASE 
       WHEN friend_count < 15 THEN 'small'
       WHEN friend_count < 30 THEN 'medium' 
       ELSE 'large'
     END as cluster_size
WITH cluster_size, collect(ego)[0..2] as sample_egos
UNWIND sample_egos as ego
MATCH p = (ego)-[:HAS_FRIEND]->(friend:Node)
RETURN p;

// ========== 6. 特征相似的用户簇 ==========
// 基于共同特征显示相似用户的网络
MATCH (ego1:EgoNode), (ego2:EgoNode)
WHERE ego1.dataset < ego2.dataset
AND size([f IN ego1.active_features WHERE f IN ego2.active_features]) >= 3
WITH ego1, ego2
LIMIT 3
MATCH p1 = (ego1)-[:HAS_FRIEND]->(friend1:Node)
MATCH p2 = (ego2)-[:HAS_FRIEND]->(friend2:Node)
RETURN p1, p2
LIMIT 40;

// ========== 7. 地理分布式多簇（按dataset分组） ==========
// 按数据集ID范围显示不同的网络簇
MATCH (ego:EgoNode)
WHERE toInteger(ego.dataset) % 10 = 1  // 选择dataset末尾为1的用户
WITH ego
LIMIT 6
MATCH p = (ego)-[:HAS_FRIEND]->(friend:Node)
RETURN p;

// ========== 8. 密度不同的网络簇 ==========
// 显示高密度和低密度的网络簇
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, collect(friend) as all_friends
WHERE size(all_friends) > 10
WITH ego, all_friends, size(all_friends) as friend_count
UNWIND all_friends as f1
UNWIND all_friends as f2
OPTIONAL MATCH (f1)-[:FRIEND_OF]-(f2)
WHERE f1.id < f2.id
WITH ego, friend_count, count(*) as connections
WITH ego, friend_count, connections,
     connections * 2.0 / (friend_count * (friend_count - 1)) as density
WHERE density > 0.1 OR density < 0.05
ORDER BY density DESC
LIMIT 4
MATCH p = (ego)-[:HAS_FRIEND]->(friend:Node)
RETURN p;

// ========== 9. 完整的多层多簇网络 ==========
// 显示EGO、朋友、朋友关系、圈子的完整多簇结构
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
WHERE friend_count > 8 AND friend_count < 18
ORDER BY rand()
LIMIT 3
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
WITH ego, collect(friend)[0..6] as sample_friends
UNWIND sample_friends as friend
MATCH p1 = (ego)-[:HAS_FRIEND]->(friend)
OPTIONAL MATCH p2 = (friend)-[:FRIEND_OF]-(other:Node)
WHERE other IN sample_friends
OPTIONAL MATCH p3 = (friend)-[:BELONGS_TO]->(circle:Circle)
RETURN p1, p2, p3;

// ========== 10. 超级网络视图 ==========
// 显示多个网络簇的概览
MATCH (ego:EgoNode)
WITH ego, rand() as r
ORDER BY r
LIMIT 8
MATCH p = (ego)-[:HAS_FRIEND]->(friend:Node)
WITH p, ego, friend
LIMIT 60
RETURN p;
