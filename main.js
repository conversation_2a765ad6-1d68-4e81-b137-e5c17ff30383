const neo4j = require('neo4j-driver');
const fs = require('fs');
const path = require('path');

// 配置信息 - 请修改这些参数
const NEO4J_URI = 'bolt://localhost:7687';
const NEO4J_USERNAME = 'neo4j';
const NEO4J_PASSWORD = '12345678'; // 修改为你的密码
const DATA_FOLDER = 'D:\\code\\graph-g6-facebook1\\twitter'; // 修改为你的数据文件夹路径

async function importToNeo4j() {
    const driver = neo4j.driver(NEO4J_URI, neo4j.auth.basic(NEO4J_USERNAME, NEO4J_PASSWORD));
    
    try {
        // 获取所有数据集ID
        const files = fs.readdirSync(DATA_FOLDER);
        const datasetIds = [...new Set(files.map(f => f.split('.')[0]))].sort();
        console.log(`找到 ${datasetIds.length} 个数据集`);
        
        const session = driver.session();
        
        // 创建约束
        await session.run('CREATE CONSTRAINT node_unique IF NOT EXISTS FOR (n:Node) REQUIRE (n.id, n.dataset) IS UNIQUE');
        
        let processed = 0;
        
        for (const datasetId of datasetIds) {
            try {
                console.log(`处理数据集 ${++processed}/${datasetIds.length}: ${datasetId}`);
                
                const featFile = path.join(DATA_FOLDER, `${datasetId}.feat`);
                const edgesFile = path.join(DATA_FOLDER, `${datasetId}.edges`);
                
                // 检查必要文件
                if (!fs.existsSync(featFile) || !fs.existsSync(edgesFile)) {
                    console.log(`跳过 ${datasetId}: 缺少必要文件`);
                    continue;
                }
                
                // 导入节点
                const featContent = fs.readFileSync(featFile, 'utf8');
                const nodes = featContent.trim().split('\n').map(line => {
                    const parts = line.split(/\s+/);
                    return {
                        id: neo4j.int(parseInt(parts[0])),
                        dataset: datasetId,
                        features: parts.slice(1).map(x => parseFloat(x))
                    };
                }).filter(node => !isNaN(node.id.toNumber()));
                
                if (nodes.length > 0) {
                    // 批量创建节点
                    const batchSize = 1000;
                    for (let i = 0; i < nodes.length; i += batchSize) {
                        const batch = nodes.slice(i, i + batchSize);
                        await session.run(`
                            UNWIND $nodes AS node
                            CREATE (n:Node {
                                id: node.id,
                                dataset: node.dataset,
                                features: node.features
                            })
                        `, { nodes: batch });
                    }
                }
                
                // 导入边
                const edgesContent = fs.readFileSync(edgesFile, 'utf8');
                const edges = edgesContent.trim().split('\n').map(line => {
                    const parts = line.split(/\s+/);
                    if (parts.length >= 2) {
                        return {
                            source: neo4j.int(parseInt(parts[0])),
                            target: neo4j.int(parseInt(parts[1])),
                            dataset: datasetId
                        };
                    }
                }).filter(edge => edge && !isNaN(edge.source.toNumber()) && !isNaN(edge.target.toNumber()));
                
                if (edges.length > 0) {
                    // 批量创建边
                    const batchSize = 1000;
                    for (let i = 0; i < edges.length; i += batchSize) {
                        const batch = edges.slice(i, i + batchSize);
                        await session.run(`
                            UNWIND $edges AS edge
                            MATCH (a:Node {id: edge.source, dataset: edge.dataset})
                            MATCH (b:Node {id: edge.target, dataset: edge.dataset})
                            CREATE (a)-[:CONNECTED]->(b)
                        `, { edges: batch });
                    }
                }
                
                console.log(`✓ 完成 ${datasetId}: ${nodes.length} 节点, ${edges.length} 边`);
                
            } catch (error) {
                console.error(`✗ 错误 ${datasetId}:`, error.message);
            }
        }
        
        await session.close();
        console.log('所有数据集导入完成!');
        
    } catch (error) {
        console.error('导入过程出错:', error);
    } finally {
        await driver.close();
    }
}

// 运行导入
importToNeo4j().catch(console.error);