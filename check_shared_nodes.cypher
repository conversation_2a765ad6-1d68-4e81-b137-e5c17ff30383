// 检查是否存在共享节点 - 同一个朋友节点属于多个EGO用户

// ========== 1. 检查朋友节点是否被多个EGO共享 ==========
MATCH (ego1:EgoNode)-[:HAS_FRIEND]->(friend:Node)<-[:HAS_FRIEND]-(ego2:EgoNode)
WHERE ego1.dataset <> ego2.dataset
RETURN ego1.dataset as ego1_dataset, 
       ego2.dataset as ego2_dataset,
       friend.id as shared_friend_id,
       friend.dataset as friend_dataset
LIMIT 20;

// ========== 2. 统计每个朋友节点被多少个EGO连接 ==========
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH friend, collect(DISTINCT ego.dataset) as connected_egos
WHERE size(connected_egos) > 1
RETURN friend.id as friend_id,
       friend.dataset as friend_dataset,
       size(connected_egos) as ego_count,
       connected_egos[0..5] as sample_egos
ORDER BY ego_count DESC
LIMIT 20;

// ========== 3. 检查节点ID的唯一性 ==========
// 检查是否有相同ID的节点在不同dataset中
MATCH (friend1:Node), (friend2:Node)
WHERE friend1.id = friend2.id AND friend1.dataset <> friend2.dataset
RETURN friend1.id as same_id,
       friend1.dataset as dataset1,
       friend2.dataset as dataset2
LIMIT 10;

// ========== 4. 检查EGO节点是否也作为朋友节点存在 ==========
// 看看EGO用户是否也是其他用户的朋友
MATCH (ego:EgoNode), (friend:Node)
WHERE ego.id = friend.id
RETURN ego.dataset as ego_dataset,
       ego.id as ego_id,
       friend.dataset as friend_dataset,
       friend.id as friend_id
LIMIT 10;

// ========== 5. 分析数据集的隔离性 ==========
// 检查每个数据集是否是独立的
MATCH (ego:EgoNode {dataset: '12831'})-[:HAS_FRIEND]->(friend:Node)
RETURN DISTINCT friend.dataset as friend_datasets
LIMIT 10;

// ========== 6. 检查跨数据集的关系 ==========
// 看看是否有跨数据集的HAS_FRIEND关系
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WHERE ego.dataset <> friend.dataset
RETURN ego.dataset as ego_dataset,
       friend.dataset as friend_dataset,
       count(*) as cross_dataset_relations
LIMIT 10;

// ========== 7. 检查FRIEND_OF关系的跨数据集情况 ==========
// 看看朋友之间的关系是否跨数据集
MATCH (friend1:Node)-[:FRIEND_OF]-(friend2:Node)
WHERE friend1.dataset <> friend2.dataset
RETURN friend1.dataset as dataset1,
       friend2.dataset as dataset2,
       count(*) as cross_relations
LIMIT 10;

// ========== 8. 完整的数据集隔离性分析 ==========
// 分析每个数据集的完整性
MATCH (ego:EgoNode)
WITH DISTINCT ego.dataset as dataset
ORDER BY dataset
LIMIT 5
MATCH (ego:EgoNode {dataset: dataset})-[:HAS_FRIEND]->(friend:Node)
OPTIONAL MATCH (friend)-[:FRIEND_OF]-(other:Node)
RETURN dataset,
       count(DISTINCT ego) as ego_count,
       count(DISTINCT friend) as friend_count,
       count(DISTINCT other) as connected_friend_count,
       collect(DISTINCT friend.dataset)[0..3] as friend_datasets;

// ========== 9. 可视化共享节点（如果存在） ==========
// 如果存在共享节点，显示它们
MATCH (ego1:EgoNode)-[:HAS_FRIEND]->(friend:Node)<-[:HAS_FRIEND]-(ego2:EgoNode)
WHERE ego1.dataset < ego2.dataset
RETURN ego1, ego2, friend
LIMIT 10;

// ========== 10. 检查节点总数 vs 唯一ID数 ==========
// 看看是否有重复的节点
MATCH (n:Node)
WITH count(n) as total_nodes, count(DISTINCT n.id) as unique_ids
RETURN total_nodes, unique_ids, (total_nodes - unique_ids) as potential_duplicates;
