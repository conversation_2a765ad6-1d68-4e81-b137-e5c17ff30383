// 多跳查询 - 从指定节点开始的多跳网络探索

// ========== 1. 从特定EGO节点开始的3跳查询 ==========
// 第1跳：EGO -> 朋友
// 第2跳：朋友 -> 朋友的朋友
// 第3跳：朋友的朋友 -> 圈子
MATCH path = (start:EgoNode {dataset: '12831'})-[*1..3]-(end)
WHERE length(path) <= 3
WITH nodes(path) as path_nodes, relationships(path) as path_rels
UNWIND path_nodes as node
UNWIND path_rels as rel

RETURN {
  nodes: collect(DISTINCT {
    id: CASE 
      WHEN 'EgoNode' IN labels(node) THEN node.id
      WHEN 'Node' IN labels(node) THEN node.id  
      WHEN 'Circle' IN labels(node) THEN 'circle_' + node.dataset + '_' + node.circle_id
      ELSE node.id
    END,
    label: CASE 
      WHEN 'EgoNode' IN labels(node) THEN 'EGO_' + node.dataset
      WHEN 'Node' IN labels(node) THEN 'Friend_' + node.id
      WHEN 'Circle' IN labels(node) THEN 'Circle_' + node.circle_id
      ELSE toString(node.id)
    END,
    type: CASE 
      WHEN 'EgoNode' IN labels(node) THEN 'ego'
      WHEN 'Node' IN labels(node) THEN 'friend'
      WHEN 'Circle' IN labels(node) THEN 'circle'
      ELSE 'unknown'
    END,
    dataset: node.dataset,
    features: CASE 
      WHEN 'EgoNode' IN labels(node) OR 'Node' IN labels(node) 
      THEN node.active_features[0..3]
      ELSE []
    END
  }),
  edges: collect(DISTINCT {
    source: CASE 
      WHEN 'EgoNode' IN labels(startNode(rel)) THEN startNode(rel).id
      WHEN 'Node' IN labels(startNode(rel)) THEN startNode(rel).id
      WHEN 'Circle' IN labels(startNode(rel)) THEN 'circle_' + startNode(rel).dataset + '_' + startNode(rel).circle_id
      ELSE startNode(rel).id
    END,
    target: CASE 
      WHEN 'EgoNode' IN labels(endNode(rel)) THEN endNode(rel).id
      WHEN 'Node' IN labels(endNode(rel)) THEN endNode(rel).id
      WHEN 'Circle' IN labels(endNode(rel)) THEN 'circle_' + endNode(rel).dataset + '_' + endNode(rel).circle_id
      ELSE endNode(rel).id
    END,
    type: type(rel),
    dataset: startNode(rel).dataset
  })
} as multi_hop_data;

// ========== 2. 简化的3跳查询（更高效） ==========
// 分步骤查询，避免复杂的路径匹配
MATCH (start:EgoNode {dataset: '12831'})

// 第1跳：获取直接朋友
MATCH (start)-[:HAS_FRIEND]->(friend1:Node)
WITH start, collect(friend1)[0..10] as first_hop_friends

// 第2跳：获取朋友的朋友
UNWIND first_hop_friends as friend1
MATCH (friend1)-[:FRIEND_OF]-(friend2:Node)
WHERE friend2.dataset = start.dataset
WITH start, first_hop_friends, collect(DISTINCT friend2)[0..15] as second_hop_friends

// 第3跳：获取圈子信息
UNWIND (first_hop_friends + second_hop_friends) as friend
MATCH (friend)-[:BELONGS_TO]->(circle:Circle)
WITH start, first_hop_friends, second_hop_friends, collect(DISTINCT circle) as circles

RETURN {
  nodes: [{
    id: start.id,
    label: 'EGO_' + start.dataset,
    type: 'ego',
    dataset: start.dataset,
    features: start.active_features[0..3],
    hop: 0
  }] + 
  [f IN first_hop_friends | {
    id: f.id,
    label: 'Friend_' + f.id,
    type: 'friend',
    dataset: f.dataset,
    features: f.active_features[0..2],
    hop: 1
  }] +
  [f IN second_hop_friends | {
    id: f.id,
    label: 'Friend_' + f.id,
    type: 'friend',
    dataset: f.dataset,
    features: f.active_features[0..2],
    hop: 2
  }] +
  [c IN circles | {
    id: 'circle_' + c.dataset + '_' + c.circle_id,
    label: 'Circle_' + c.circle_id,
    type: 'circle',
    dataset: c.dataset,
    circle_id: c.circle_id,
    size: c.size,
    hop: 3
  }],
  
  edges: [f IN first_hop_friends | {
    source: start.id,
    target: f.id,
    type: 'HAS_FRIEND',
    dataset: start.dataset
  }] +
  [f1 IN first_hop_friends | 
    [f2 IN second_hop_friends WHERE EXISTS((f1)-[:FRIEND_OF]-(f2)) | {
      source: f1.id,
      target: f2.id,
      type: 'FRIEND_OF',
      dataset: f1.dataset
    }]
  ][0] +
  [c IN circles |
    [f IN (first_hop_friends + second_hop_friends) WHERE EXISTS((f)-[:BELONGS_TO]->(c)) | {
      source: f.id,
      target: 'circle_' + c.dataset + '_' + c.circle_id,
      type: 'BELONGS_TO',
      dataset: c.dataset
    }]
  ][0]
} as three_hop_network;

// ========== 3. 可配置的多跳查询 ==========
// 可以调整起始节点和跳数
WITH '12831' as start_dataset, 2 as max_hops

MATCH (start:EgoNode {dataset: start_dataset})
CALL {
  WITH start, max_hops
  MATCH path = (start)-[*1..max_hops]-(end)
  RETURN collect(DISTINCT nodes(path)) as all_path_nodes,
         collect(DISTINCT relationships(path)) as all_path_rels
}

WITH start, all_path_nodes, all_path_rels
UNWIND all_path_nodes as path_nodes_list
UNWIND path_nodes_list as node
WITH start, collect(DISTINCT node) as unique_nodes, all_path_rels
UNWIND all_path_rels as path_rels_list  
UNWIND path_rels_list as rel

RETURN {
  start_node: start.dataset,
  max_hops: max_hops,
  nodes: [n IN unique_nodes | {
    id: CASE 
      WHEN 'EgoNode' IN labels(n) THEN n.id
      WHEN 'Node' IN labels(n) THEN n.id
      WHEN 'Circle' IN labels(n) THEN 'circle_' + n.dataset + '_' + n.circle_id
      ELSE n.id
    END,
    type: CASE 
      WHEN 'EgoNode' IN labels(n) THEN 'ego'
      WHEN 'Node' IN labels(n) THEN 'friend'
      WHEN 'Circle' IN labels(n) THEN 'circle'
      ELSE 'unknown'
    END,
    dataset: n.dataset
  }],
  edges: collect(DISTINCT {
    source: CASE 
      WHEN 'Circle' IN labels(startNode(rel)) 
      THEN 'circle_' + startNode(rel).dataset + '_' + startNode(rel).circle_id
      ELSE startNode(rel).id
    END,
    target: CASE 
      WHEN 'Circle' IN labels(endNode(rel)) 
      THEN 'circle_' + endNode(rel).dataset + '_' + endNode(rel).circle_id
      ELSE endNode(rel).id
    END,
    type: type(rel)
  })
} as configurable_multi_hop;

// ========== 4. 从朋友节点开始的多跳查询 ==========
// 从特定朋友节点开始探索
MATCH (start:Node {id: 27985216, dataset: '12831'})

// 1跳：找到这个朋友的EGO用户
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(start)

// 2跳：找到这个朋友的其他朋友
MATCH (start)-[:FRIEND_OF]-(other_friend:Node)
WHERE other_friend.dataset = start.dataset

// 3跳：找到相关的圈子
MATCH (start)-[:BELONGS_TO]->(circle:Circle)

RETURN {
  start_friend: start.id,
  nodes: [{
    id: ego.id,
    type: 'ego',
    label: 'EGO_' + ego.dataset,
    dataset: ego.dataset
  }, {
    id: start.id,
    type: 'friend',
    label: 'Start_' + start.id,
    dataset: start.dataset
  }] + 
  collect(DISTINCT {
    id: other_friend.id,
    type: 'friend',
    label: 'Friend_' + other_friend.id,
    dataset: other_friend.dataset
  }) +
  collect(DISTINCT {
    id: 'circle_' + circle.dataset + '_' + circle.circle_id,
    type: 'circle',
    label: 'Circle_' + circle.circle_id,
    dataset: circle.dataset
  }),
  
  edges: [{
    source: ego.id,
    target: start.id,
    type: 'HAS_FRIEND'
  }] +
  collect(DISTINCT {
    source: start.id,
    target: other_friend.id,
    type: 'FRIEND_OF'
  }) +
  collect(DISTINCT {
    source: start.id,
    target: 'circle_' + circle.dataset + '_' + circle.circle_id,
    type: 'BELONGS_TO'
  })
} as friend_centered_multi_hop;
