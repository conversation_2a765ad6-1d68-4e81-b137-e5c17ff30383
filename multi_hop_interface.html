<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Twitter多跳网络可视化</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 350px;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            overflow-y: auto;
        }
        
        .main-content {
            flex: 1;
            position: relative;
        }
        
        #multi-hop-container {
            width: 100%;
            height: 100%;
            background-color: #ecf0f1;
        }
        
        .controls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: #2980b9;
        }
        
        .btn.danger {
            background-color: #e74c3c;
        }
        
        .btn.danger:hover {
            background-color: #c0392b;
        }
        
        .input-section {
            background-color: #34495e;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .input-section h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #bdc3c7;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 3px;
            background-color: #ecf0f1;
            color: #2c3e50;
        }
        
        .stats {
            background-color: #34495e;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .stats h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .stats p {
            margin: 5px 0;
            color: #bdc3c7;
        }
        
        #multi-hop-details {
            background-color: #34495e;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        
        #multi-hop-details h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        #multi-hop-details p {
            margin: 8px 0;
            color: #bdc3c7;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0,0,0,0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 2000;
        }
        
        .legend {
            background-color: #34495e;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .legend h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .ego-color { background-color: #e74c3c; }
        .friend-color { background-color: #3498db; }
        .circle-color { background-color: #9b59b6; }
        
        .examples {
            background-color: #34495e;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .examples h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .example-btn {
            background-color: #27ae60;
            color: white;
            border: none;
            padding: 5px 10px;
            margin: 3px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .example-btn:hover {
            background-color: #229954;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h2>多跳网络查询</h2>
            
            <div class="input-section">
                <h3>查询参数</h3>
                
                <div class="form-group">
                    <label for="nodeId">节点ID:</label>
                    <input type="text" id="nodeId" placeholder="例如: 12831" value="12831">
                </div>
                
                <div class="form-group">
                    <label for="maxHops">最大跳数:</label>
                    <select id="maxHops">
                        <option value="1">1跳</option>
                        <option value="2">2跳</option>
                        <option value="3" selected>3跳</option>
                        <option value="4">4跳</option>
                    </select>
                </div>
                
                <button class="btn" onclick="executeMultiHopQuery()">执行查询</button>
            </div>
            
            <div class="examples">
                <h3>示例查询</h3>
                <button class="example-btn" onclick="loadExample('12831', 3)">
                    节点: 12831
                </button>
                <button class="example-btn" onclick="loadExample('27985216', 3)">
                    节点: 27985216
                </button>
                <button class="example-btn" onclick="loadExample('1046661', 2)">
                    节点: 1046661
                </button>
                <button class="example-btn" onclick="loadExample('41494136', 4)">
                    节点: 41494136
                </button>
            </div>
            
            <div class="legend">
                <h3>图例</h3>
                <div class="legend-item">
                    <div class="legend-color ego-color"></div>
                    <span>EGO用户（起始节点）</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color friend-color"></div>
                    <span>朋友节点</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color circle-color"></div>
                    <span>社交圈子</span>
                </div>
            </div>
            
            <div class="stats" id="multi-hop-stats">
                <h3>网络统计</h3>
                <p>请执行查询查看统计信息</p>
            </div>
            
            <div id="multi-hop-details">
                <h3>节点详情</h3>
                <p>点击节点查看详细信息</p>
            </div>
        </div>
        
        <div class="main-content">
            <div class="controls">
                <button class="btn" onclick="executeMultiHopQuery()">执行查询</button>
                <button class="btn" onclick="zoomToFitMultiHop()">适应窗口</button>
                <button class="btn danger" onclick="resetMultiHopGraph()">重置图形</button>
            </div>
            
            <div id="multi-hop-container"></div>
            
            <div id="loading" class="loading" style="display: none;">
                正在执行多跳查询...
            </div>
        </div>
    </div>

    <!-- Sigma.js 库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sigma.js/1.2.1/sigma.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sigma.js/1.2.1/plugins/sigma.layout.forceAtlas2.min.js"></script>
    
    <!-- 多跳可视化脚本 -->
    <script src="multi_hop_visualization.js"></script>
    
    <script>
        // 加载示例
        function loadExample(nodeId, maxHops) {
            document.getElementById('nodeId').value = nodeId;
            document.getElementById('maxHops').value = maxHops;
        }
        
        // 执行多跳查询
        function executeMultiHopQuery() {
            const nodeId = document.getElementById('nodeId').value;
            const maxHops = parseInt(document.getElementById('maxHops').value);

            if (!nodeId) {
                alert('请输入节点ID');
                return;
            }

            initMultiHopVisualization(nodeId, maxHops);
        }
        
        // 适应窗口
        function zoomToFitMultiHop() {
            if (window.multiHopSigma) {
                window.multiHopSigma.camera.goTo({ x: 0, y: 0, angle: 0, ratio: 1 });
            }
        }
        
        // 重置图形
        function resetMultiHopGraph() {
            if (window.multiHopSigma) {
                window.multiHopSigma.graph.clear();
                window.multiHopSigma.refresh();
            }
            document.getElementById('multi-hop-stats').innerHTML = '<h3>网络统计</h3><p>请执行查询查看统计信息</p>';
            document.getElementById('multi-hop-details').innerHTML = '<h3>节点详情</h3><p>点击节点查看详细信息</p>';
        }
        
        // 显示/隐藏加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('多跳网络查询界面已准备就绪');
        });
    </script>
</body>
</html>
