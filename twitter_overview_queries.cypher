// Twitter数据全貌查询 - 展示ego节点和朋友关系网络

// ========== 1. 数据概览查询 ==========

// 查看所有节点类型和数量
MATCH (n) 
RETURN labels(n)[0] as NodeType, count(n) as Count
ORDER BY Count DESC;

// 查看所有关系类型和数量
MATCH ()-[r]->() 
RETURN type(r) as RelationType, count(r) as Count
ORDER BY Count DESC;

// ========== 2. 单个用户完整网络展示 ==========

// 展示特定用户的完整社交网络（以12831为例）
MATCH (ego:EgoNode {dataset: '12831'})
OPTIONAL MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
OPTIONAL MATCH (friend)-[friendship:FRIEND_OF]-(other_friend:Node)
WHERE other_friend.dataset = '12831'
RETURN ego, friend, friendship, other_friend
LIMIT 100;

// ========== 3. 多用户网络对比展示 ==========

// 展示前3个最活跃用户的网络结构
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
ORDER BY friend_count DESC LIMIT 3
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
OPTIONAL MATCH (friend)-[friendship:FRIEND_OF]-(other_friend:Node)
WHERE other_friend.dataset = ego.dataset
RETURN ego.dataset as EgoUser, 
       ego, 
       friend, 
       friendship, 
       other_friend,
       friend_count
ORDER BY friend_count DESC;

// ========== 4. 网络密度分析查询 ==========

// 分析每个用户的网络密度
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, collect(friend) as friends
WHERE size(friends) > 1
UNWIND friends as f1
UNWIND friends as f2
OPTIONAL MATCH (f1)-[:FRIEND_OF]-(f2)
WHERE f1.id < f2.id AND f1.dataset = ego.dataset AND f2.dataset = ego.dataset
WITH ego, 
     size(friends) as total_friends,
     count(DISTINCT f1) + count(DISTINCT f2) as connected_pairs,
     size(friends) * (size(friends) - 1) / 2 as possible_connections
RETURN ego.dataset,
       total_friends,
       connected_pairs,
       possible_connections,
       round(connected_pairs * 100.0 / possible_connections, 2) as density_percent
ORDER BY density_percent DESC
LIMIT 10;

// ========== 5. 可视化友好的查询 ==========

// 小规模网络展示（适合可视化）
MATCH (ego:EgoNode)
WITH ego, rand() as r
ORDER BY r LIMIT 1
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
WITH ego, collect(friend)[0..10] as sample_friends
UNWIND sample_friends as friend
OPTIONAL MATCH (friend)-[friendship:FRIEND_OF]-(other_friend:Node)
WHERE other_friend IN sample_friends
RETURN ego, friend, friendship, other_friend;

// ========== 6. 社交圈子网络展示 ==========

// 展示用户的社交圈子结构
MATCH (ego:EgoNode {dataset: '12831'})
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)-[:BELONGS_TO]->(circle:Circle)
RETURN ego.dataset as User,
       circle.circle_id as CircleID,
       circle.size as CircleSize,
       collect(friend.id)[0..5] as SampleFriends
ORDER BY CircleSize DESC;

// ========== 7. 特征相似性网络 ==========

// 基于共同特征的用户相似性
MATCH (ego1:EgoNode), (ego2:EgoNode)
WHERE ego1.dataset < ego2.dataset
AND size(ego1.active_features) > 0 
AND size(ego2.active_features) > 0
WITH ego1, ego2, 
     [f IN ego1.active_features WHERE f IN ego2.active_features] as common_features
WHERE size(common_features) >= 3
RETURN ego1.dataset, ego2.dataset,
       size(ego1.active_features) as ego1_features,
       size(ego2.active_features) as ego2_features,
       size(common_features) as common_features,
       common_features[0..3] as sample_common_features
ORDER BY size(common_features) DESC
LIMIT 20;

// ========== 8. 完整路径查询 ==========

// 查找用户间的最短路径
MATCH (ego1:EgoNode {dataset: '12831'}), (ego2:EgoNode {dataset: '1046661'})
MATCH path = shortestPath((ego1)-[*..4]-(ego2))
RETURN path;

// ========== 9. 影响力分析 ==========

// 查找最有影响力的朋友节点（连接最多朋友的节点）
MATCH (friend:Node)-[:FRIEND_OF]-(other:Node)
WHERE friend.dataset = other.dataset
WITH friend, count(other) as connections
ORDER BY connections DESC
LIMIT 10
MATCH (ego:EgoNode {dataset: friend.dataset})-[:HAS_FRIEND]->(friend)
RETURN ego.dataset as User,
       friend.id as InfluentialFriend,
       connections as FriendConnections;

// ========== 10. 综合网络概览 ==========

// 数据库整体统计
MATCH (ego:EgoNode)
OPTIONAL MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
OPTIONAL MATCH (friend)-[:FRIEND_OF]-(other:Node)
WHERE other.dataset = ego.dataset
OPTIONAL MATCH (friend)-[:BELONGS_TO]->(circle:Circle)
RETURN ego.dataset as User,
       count(DISTINCT friend) as TotalFriends,
       count(DISTINCT other) as ConnectedFriends,
       count(DISTINCT circle) as Circles,
       size(ego.active_features) as ActiveFeatures
ORDER BY TotalFriends DESC
LIMIT 20;
