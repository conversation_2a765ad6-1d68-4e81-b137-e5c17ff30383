// 图形可视化查询 - 返回节点和关系以在Neo4j Browser中显示图形

// ========== 1. 展示EGO用户和朋友的完整网络 ==========
// 选择一个用户展示其完整社交网络
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WHERE ego.dataset = '12831'  // 可以改成其他用户ID
OPTIONAL MATCH (friend)-[friendship:FRIEND_OF]-(other_friend:Node)
WHERE other_friend.dataset = ego.dataset
RETURN ego, friend, friendship, other_friend
LIMIT 50;

// ========== 2. 展示社交圈子网络 ==========
// 显示朋友节点和它们所属的圈子
MATCH p=(friend:Node)-[:BELONGS_TO]->(circle:Circle)
WHERE friend.dataset = '12831'  // 可以改成其他用户ID
RETURN p
LIMIT 25;

// ========== 3. 展示完整的三层网络结构 ==========
// EGO -> 朋友 -> 圈子的完整路径
MATCH p1=(ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
MATCH p2=(friend)-[:BELONGS_TO]->(circle:Circle)
WHERE ego.dataset = '12831'  // 可以改成其他用户ID
RETURN p1, p2
LIMIT 30;

// ========== 4. 展示朋友关系网络 ==========
// 只显示朋友之间的关系
MATCH (ego:EgoNode {dataset: '12831'})-[:HAS_FRIEND]->(friend1:Node)
MATCH (ego)-[:HAS_FRIEND]->(friend2:Node)
MATCH p=(friend1)-[:FRIEND_OF]-(friend2)
RETURN p
LIMIT 25;

// ========== 5. 展示随机用户的完整网络 ==========
// 随机选择一个用户展示网络
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
WHERE friend_count > 5 AND friend_count < 30
ORDER BY rand()
LIMIT 1
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
OPTIONAL MATCH (friend)-[rel:FRIEND_OF]-(other:Node)
WHERE other.dataset = ego.dataset
RETURN ego, friend, rel, other;

// ========== 6. 展示多个用户的对比网络 ==========
// 显示前3个最活跃用户的网络
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
ORDER BY friend_count DESC
LIMIT 3
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
RETURN ego, friend
LIMIT 50;

// ========== 7. 展示特征网络 ==========
// 显示用户和其特征的关系（如果有FeatureName节点）
MATCH (ego:EgoNode {dataset: '12831'})
MATCH (feat:FeatureName {dataset: '12831'})
WHERE feat.name IN ego.active_features[0..10]
RETURN ego, feat
LIMIT 20;

// ========== 8. 展示完整的四层网络 ==========
// EGO -> 朋友 -> 朋友关系 -> 圈子
MATCH (ego:EgoNode {dataset: '12831'})
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
OPTIONAL MATCH (friend)-[friendship:FRIEND_OF]-(other_friend:Node)
WHERE other_friend.dataset = ego.dataset
OPTIONAL MATCH (friend)-[:BELONGS_TO]->(circle:Circle)
RETURN ego, friend, friendship, other_friend, circle
LIMIT 40;

// ========== 9. 展示网络子图 ==========
// 选择一个朋友数适中的用户展示子网络
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, collect(friend) as all_friends
WHERE size(all_friends) > 10 AND size(all_friends) < 25
WITH ego, all_friends[0..12] as sample_friends
UNWIND sample_friends as friend
MATCH (ego)-[:HAS_FRIEND]->(friend)
OPTIONAL MATCH (friend)-[rel:FRIEND_OF]-(other)
WHERE other IN sample_friends
RETURN ego, friend, rel, other;

// ========== 10. 展示所有关系类型的样本 ==========
// 显示数据库中所有类型的关系
MATCH p=()-[:HAS_FRIEND]->()
RETURN p
LIMIT 10
UNION ALL
MATCH p=()-[:FRIEND_OF]-()
RETURN p
LIMIT 10
UNION ALL
MATCH p=()-[:BELONGS_TO]->()
RETURN p
LIMIT 10;
