// Twitter数据Neo4j查询示例

// 1. 查看数据概览
MATCH (n) RETURN labels(n) as NodeType, count(n) as Count;

// 2. 查找最活跃的用户（朋友最多）
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
RETURN ego.id, ego.dataset, count(friend) as friend_count
ORDER BY friend_count DESC LIMIT 10;

// 3. 查找共同朋友最多的用户对
MATCH (ego1:EgoNode)-[:HAS_FRIEND]->(common:Node)<-[:HAS_FRIEND]-(ego2:EgoNode)
WHERE ego1.id < ego2.id
RETURN ego1.dataset, ego2.dataset, count(common) as common_friends
ORDER BY common_friends DESC LIMIT 10;

// 4. 分析社交圈子
MATCH (c:Circle)
RETURN c.dataset, c.circle_id, c.size
ORDER BY c.size DESC LIMIT 20;

// 5. 查找特征相似的用户
MATCH (ego1:EgoNode), (ego2:EgoNode)
WHERE ego1.id <> ego2.id
AND size([f IN ego1.active_features WHERE f IN ego2.active_features]) > 5
RETURN ego1.dataset, ego2.dataset, 
       size([f IN ego1.active_features WHERE f IN ego2.active_features]) as common_features
ORDER BY common_features DESC LIMIT 10;

// 6. 分析朋友网络密度
MATCH (ego:EgoNode {dataset: '12831'})-[:HAS_FRIEND]->(friend:Node)
WITH ego, collect(friend) as friends
UNWIND friends as f1
UNWIND friends as f2
MATCH (f1)-[:FRIEND_OF]-(f2)
WHERE f1.id < f2.id
RETURN ego.dataset, 
       size(friends) as total_friends,
       count(*) as friend_connections,
       round(count(*) * 2.0 / (size(friends) * (size(friends) - 1)) * 100, 2) as density_percent;

// 7. 查找桥接节点（连接不同圈子的朋友）
MATCH (friend:Node)-[:BELONGS_TO]->(c:Circle)
WITH friend, count(c) as circle_count
WHERE circle_count > 1
MATCH (friend)-[:BELONGS_TO]->(circles:Circle)
RETURN friend.id, friend.dataset, circle_count, collect(circles.circle_id) as circles
ORDER BY circle_count DESC LIMIT 10;
