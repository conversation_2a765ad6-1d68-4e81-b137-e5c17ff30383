// 最佳图形展示查询 - 一次性展示完整的Twitter社交网络

// 选择一个朋友数适中的用户，展示完整的社交网络结构
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
WHERE friend_count > 8 AND friend_count < 25
ORDER BY friend_count DESC
LIMIT 1

// 获取该用户的完整网络：EGO、朋友、朋友关系、社交圈子
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
OPTIONAL MATCH (friend)-[friendship:FRIEND_OF]-(other_friend:Node)
WHERE other_friend.dataset = ego.dataset
OPTIONAL MATCH (friend)-[:BELONGS_TO]->(circle:Circle)

// 返回所有节点和关系以显示图形
RETURN ego, friend, friendship, other_friend, circle;
