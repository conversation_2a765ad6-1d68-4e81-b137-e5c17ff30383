// 优化的多簇可视化 - 基于数据完全隔离的特点

// ========== 1. 最佳多簇展示 ==========
// 显示多个独立的社交网络簇
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
WHERE friend_count > 8 AND friend_count < 25
ORDER BY friend_count DESC
LIMIT 6
MATCH p = (ego)-[:HAS_FRIEND]->(friend:Node)
RETURN p;

// ========== 2. 不同规模的网络簇对比 ==========
// 显示小、中、大不同规模的独立网络
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
WHERE friend_count > 5
ORDER BY friend_count
WITH collect(ego) as all_egos
WITH all_egos[0..2] as small_egos,     // 小网络
     all_egos[size(all_egos)/2..size(all_egos)/2+2] as medium_egos,  // 中等网络
     all_egos[size(all_egos)-3..] as large_egos     // 大网络
WITH small_egos + medium_egos + large_egos as selected_egos
UNWIND selected_egos as ego
MATCH p = (ego)-[:HAS_FRIEND]->(friend:Node)
RETURN p;

// ========== 3. 完整的多层多簇网络 ==========
// 显示EGO、朋友、朋友关系、圈子的完整结构
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
WHERE friend_count > 10 AND friend_count < 20
ORDER BY rand()
LIMIT 4
MATCH (ego)-[:HAS_FRIEND]->(friend:Node)
WITH ego, collect(friend)[0..8] as sample_friends
UNWIND sample_friends as friend
MATCH p1 = (ego)-[:HAS_FRIEND]->(friend)
OPTIONAL MATCH p2 = (friend)-[:FRIEND_OF]-(other:Node)
WHERE other IN sample_friends
OPTIONAL MATCH p3 = (friend)-[:BELONGS_TO]->(circle:Circle)
RETURN p1, p2, p3;

// ========== 4. 高密度 vs 低密度网络簇 ==========
// 对比不同密度的独立网络
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, collect(friend) as all_friends
WHERE size(all_friends) > 12 AND size(all_friends) < 25
WITH ego, all_friends, size(all_friends) as friend_count
UNWIND all_friends as f1
UNWIND all_friends as f2
OPTIONAL MATCH (f1)-[:FRIEND_OF]-(f2)
WHERE f1.id < f2.id
WITH ego, friend_count, count(*) as connections
WITH ego, connections * 2.0 / (friend_count * (friend_count - 1)) as density
ORDER BY density
WITH collect(ego) as all_egos
WITH all_egos[0..2] as low_density,    // 低密度网络
     all_egos[size(all_egos)-3..] as high_density  // 高密度网络
WITH low_density + high_density as selected_egos
UNWIND selected_egos as ego
MATCH p = (ego)-[:HAS_FRIEND]->(friend:Node)
RETURN p;

// ========== 5. 社交圈子丰富的网络簇 ==========
// 显示有多个社交圈子的网络
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)-[:BELONGS_TO]->(circle:Circle)
WITH ego, count(DISTINCT circle) as circle_count
WHERE circle_count > 3
ORDER BY circle_count DESC
LIMIT 4
MATCH p1 = (ego)-[:HAS_FRIEND]->(friend:Node)
MATCH p2 = (friend)-[:BELONGS_TO]->(circle:Circle)
RETURN p1, p2;

// ========== 6. 特征活跃度对比的网络簇 ==========
// 基于用户特征活跃度选择不同的网络
MATCH (ego:EgoNode)
WHERE size(ego.active_features) > 5
WITH ego, size(ego.active_features) as feature_count
ORDER BY feature_count
WITH collect(ego) as all_egos
WITH all_egos[0..2] as low_feature,    // 特征少的用户
     all_egos[size(all_egos)-3..] as high_feature  // 特征多的用户
WITH low_feature + high_feature as selected_egos
UNWIND selected_egos as ego
MATCH p = (ego)-[:HAS_FRIEND]->(friend:Node)
RETURN p;

// ========== 7. 均匀分布的网络簇样本 ==========
// 从所有网络中均匀选择样本
MATCH (ego:EgoNode)
WITH ego, rand() as r
ORDER BY r
LIMIT 8
MATCH p = (ego)-[:HAS_FRIEND]->(friend:Node)
WITH p LIMIT 80  // 控制总节点数
RETURN p;

// ========== 8. 网络复杂度梯度展示 ==========
// 按网络复杂度递增展示
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
ORDER BY friend_count
WITH collect(ego) as sorted_egos
WITH [sorted_egos[5], sorted_egos[15], sorted_egos[25], sorted_egos[35], sorted_egos[45]] as selected_egos
UNWIND selected_egos as ego
WHERE ego IS NOT NULL
MATCH p = (ego)-[:HAS_FRIEND]->(friend:Node)
RETURN p;

// ========== 9. 完整的生态系统视图 ==========
// 显示整个Twitter数据的生态系统
MATCH (ego:EgoNode)-[:HAS_FRIEND]->(friend:Node)
WITH ego, count(friend) as friend_count
WHERE friend_count > 6 AND friend_count < 30
WITH ego, friend_count, 
     CASE 
       WHEN friend_count < 12 THEN 1
       WHEN friend_count < 20 THEN 2
       ELSE 3
     END as tier
WITH tier, collect(ego)[0..2] as tier_egos
UNWIND tier_egos as ego
MATCH p = (ego)-[:HAS_FRIEND]->(friend:Node)
RETURN p;

// ========== 10. 精选网络展示 ==========
// 手工精选最具代表性的网络簇
MATCH (ego:EgoNode)
WHERE ego.dataset IN ['12831', '1046661', '10798802', '1239671', '1271901', '356963']
MATCH p = (ego)-[:HAS_FRIEND]->(friend:Node)
RETURN p;
