
const fs = require('fs');

console.log('开始读取文件...');
const filePath = './full.json';
fs.readFile(filePath, 'utf8', (err, data) => {
  if (err) {
    console.error('读取文件出错:', err);
    return;
  }
  const lines = data.split('\n');
  let firstObj = null;
  let count = 0;
  for (const line of lines) {
    if (!line.trim()) continue;
    try {
      const obj = JSON.parse(line);
      count++;
      if (!firstObj) {
        firstObj = obj;
      }
    } catch (e) {
      // 跳过非 JSON 行
    }
  }
  if (firstObj) {
    console.log('第1行结构:', Object.keys(firstObj));
    console.log('示例:', firstObj);
  } else {
    console.log('没有有效的 JSON 行');
  }
  console.log('读取结束, 总行数:', count);
});
